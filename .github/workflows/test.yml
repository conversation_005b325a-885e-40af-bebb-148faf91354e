name: CI - APP

on:
  pull_request:
    branches: ['*']
  push:
    branches: ['dev', 'staging', 'main']

jobs:
  test:
    name: App Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      # checkout the repository
      - name: Checkout repository
        uses: actions/checkout@v2

      # setup node
      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          cache-dependency-path: package-lock.json
          node-version: ${{ matrix.node-version }}
          cache: npm

      # install dependencies
      - name: Install dependencies
        run: npm ci

      # run code formatter
      - name: Code Formatter
        run: npm run format

      # run lint test
      - name: Lint Test
        run: npm run lint

      # run unit test
      - name: Unit Test
        run: npm run test
