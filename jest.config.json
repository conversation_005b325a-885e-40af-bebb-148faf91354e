{"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"], "collectCoverage": true, "collectCoverageFrom": ["**/*.{js,jsx}", "!**/coverage/**", "!**/node_modules/**", "!**/babel.config.js", "!**/jest.setup.js"], "coverageReporters": ["text-summary"], "projects": [{"preset": "jest-expo/ios"}, {"preset": "jest-expo/android"}, {"preset": "jest-expo/web"}]}