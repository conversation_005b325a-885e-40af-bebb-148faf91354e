import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StatusBar } from 'expo-status-bar';
import * as SplashScreen from 'expo-splash-screen';

import Navigator from './src/navigator';
import store, { initializeStore } from './src/utils/store';

// Keep splash screen visible during initialization
SplashScreen.preventAutoHideAsync();

export default function App() {
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('MiddleCrop: Initializing app...');

        // Initialize MiddleCrop store with persisted data and purchases
        await initializeStore();

        console.log('MiddleCrop: App initialization complete');

        // Hide splash screen after initialization
        await SplashScreen.hideAsync();
      } catch (error) {
        console.error('MiddleCrop: App initialization failed:', error);

        // Hide splash screen even if initialization fails
        await SplashScreen.hideAsync();
      }
    };

    initializeApp();
  }, []);

  return (
    <Provider store={store}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <Navigator />
        <StatusBar style="auto" />
      </GestureHandlerRootView>
    </Provider>
  );
}
