{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "module": "esnext",
    "target": "es2020",
    "baseUrl": "./",
    "paths": {
      "@assets/*": ["./assets/*"],
      "@components/*": ["./src/components/*"],
      "@views/*": ["./src/views/*"],
      "@layouts/*": ["./src/layouts/*"],
      "@hooks": ["./src/hooks/index.ts"],
      "@navigator": ["./src/navigator/index.ts"],
      "@navigator/*": ["./src/navigator/*"],
      "@utils/*": ["./src/utils/*"],
      "@theme": ["./src/theme/index.ts"],
      "@modules/*": ["./src/modules/*"],
    }
  }
}
