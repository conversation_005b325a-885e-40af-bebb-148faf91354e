{"name": "middlecrop", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"ci": "watchman watch-del-all && rm -rf node_modules && npm install", "prestart": "watchman watch-del-all", "start": "NODE_ENV=production npx expo start -c", "start:dev": "NODE_ENV=development npx expo start -c", "android": "NODE_ENV=production npx expo start --android", "android:dev": "NODE_ENV=development npx expo start --android", "ios": "NODE_ENV=production npx expo start --ios", "ios:dev": "NODE_ENV=development npx expo start --ios", "web": "NODE_ENV=production npx expo start --web", "web:dev": "NODE_ENV=development npx expo start --web", "format": "prettier --config .prettierrc src/**/*.{ts,tsx} --write", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "jest --verbose --passWithNoTests", "test:watch": "jest --verbose --passWithNoTests --watch", "prepare": "husky install"}, "dependencies": {"@expo/metro-runtime": "~3.1.3", "@gorhom/bottom-sheet": "^4", "@react-native-async-storage/async-storage": "1.21.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^1.9.7", "@shopify/react-native-skia": "0.1.221", "babel-plugin-module-resolver": "^5.0.0", "expo": "50.0.21", "expo-asset": "~9.0.2", "expo-constants": "~15.4.5", "expo-crypto": "~12.8.1", "expo-dynamic-image-crop": "^1.4.44", "expo-file-system": "~16.0.9", "expo-font": "~11.10.3", "expo-haptics": "~12.8.1", "expo-image": "~1.10.6", "expo-image-manipulator": "~11.8.0", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.2", "expo-sharing": "~11.10.0", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-updates": "~0.24.11", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-gesture-handler": "~2.14.0", "react-native-purchases": "^9.0.0", "react-native-purchases-ui": "^9.0.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-web": "~0.19.6", "react-redux": "^8.1.3", "redux-logger": "^3.0.6"}, "devDependencies": {"@babel/core": "^7.23.3", "@types/jest": "^29.5.8", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.6", "@types/redux-logger": "^3.0.12", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.53.0", "eslint-config-universe": "^12.0.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jest": "^29.3.1", "jest-expo": "~50.0.2", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "ts-node": "^10.9.1", "typescript": "^5.3.0"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}