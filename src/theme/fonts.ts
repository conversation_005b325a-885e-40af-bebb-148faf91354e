import { getFontFamily } from '../utils/fontLoader';

export const fonts = {
  regular: getFontFamily('OpenSans-Regular'),
  bold: getFontFamily('OpenSans-Bold'),
  medium: getFontFamily('OpenSans-Semibold'), // Using Semibold for medium weight
  semibold: getFontFamily('OpenSans-Semibold'),
  italic: getFontFamily('OpenSans-Italic'),
  boldItalic: getFontFamily('OpenSans-BoldItalic'),
  semiboldItalic: getFontFamily('OpenSans-SemiboldItalic'),

  // OpenSans font family structure (for compatibility)
  openSan: {
    regular: getFontFamily('OpenSans-Regular'),
    bold: getFontFamily('OpenSans-Bold'),
    medium: getFontFamily('OpenSans-Semibold'), // Using Semibold for medium weight
    semibold: getFontFamily('OpenSans-Semibold'),
    italic: getFontFamily('OpenSans-Italic'),
    boldItalic: getFontFamily('OpenSans-BoldItalic'),
    semiboldItalic: getFontFamily('OpenSans-SemiboldItalic'),
  },

  // Font sizes
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },

  // Font weights
  weights: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};

export default fonts;
