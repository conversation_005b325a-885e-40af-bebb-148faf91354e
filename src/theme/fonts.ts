export const fonts = {
  regular: 'OpenSans-Regular',
  bold: 'OpenSans-Bold',
  medium: 'OpenSans-Medium',
  semibold: 'OpenSans-Semibold',
  italic: 'OpenSans-Italic',
  boldItalic: 'OpenSans-BoldItalic',
  semiboldItalic: 'OpenSans-SemiboldItalic',

  // OpenSans font family structure (for compatibility)
  openSan: {
    regular: 'OpenSans-Regular',
    bold: 'OpenSans-Bold',
    medium: 'OpenSans-Medium',
    semibold: 'OpenSans-Semibold',
    italic: 'OpenSans-Italic',
    boldItalic: 'OpenSans-BoldItalic',
    semiboldItalic: 'OpenSans-SemiboldItalic',
  },

  // Font sizes
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },

  // Font weights
  weights: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};

export default fonts;
