export const colors = {
  // MiddleCrop brand colors
  primary: '#1E40AF', // CapitalClientsMedia blue
  secondary: '#7C3AED', // Premium purple
  accent: '#059669', // Success green
  warning: '#D97706', // Warning amber
  error: '#DC2626', // Error red

  // UI colors
  background: '#ffffff',
  surface: '#f8fafc',
  text: '#1f2937',
  textSecondary: '#6b7280',
  border: '#e5e7eb',
  disabled: '#9ca3af',

  // Legacy colors (keeping for compatibility)
  darkPurple: '#231d54',
  purple: '#8100ff',
  lightPurple: '#9388db',
  lightGrayPurple: '#e5e7eb', // Alternative name for lightPurple
  lightGray: '#f3f4f6',
  gray: '#6b7280',
  darkGray: '#374151',

  // Basic colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
  pink: '#ec4899',

  // MiddleCrop specific
  cropOverlay: 'rgba(0, 0, 0, 0.6)',
  cropHandle: '#ffffff',
  cropBorder: '#1E40AF',

  // Status colors
  success: '#10b981',
  info: '#3b82f6',

  // Gradient colors
  gradientStart: '#1E40AF',
  gradientEnd: '#7C3AED',
};

export default colors;
