import * as Font from 'expo-font';

// Font mapping for OpenSans family
// Note: We don't have OpenSans-Medium.ttf, so we use Semibold for medium weight
const fontMap = {
  'OpenSans-Regular': require('../../assets/fonts/OpenSans-Regular.ttf'),
  'OpenSans-Bold': require('../../assets/fonts/OpenSans-Bold.ttf'),
  'OpenSans-Semibold': require('../../assets/fonts/OpenSans-Semibold.ttf'),
  'OpenSans-Italic': require('../../assets/fonts/OpenSans-Italic.ttf'),
  'OpenSans-BoldItalic': require('../../assets/fonts/OpenSans-BoldItalic.ttf'),
  'OpenSans-SemiboldItalic': require('../../assets/fonts/OpenSans-SemiboldItalic.ttf'),
};

// Check if fonts are loaded
let fontsLoaded = false;

/**
 * Load all OpenSans fonts
 * @returns Promise that resolves when fonts are loaded
 */
export const loadFonts = async (): Promise<void> => {
  if (fontsLoaded) {
    return; // Fonts already loaded
  }

  try {
    console.log('MiddleCrop: Loading fonts...');
    await Font.loadAsync(fontMap);
    fontsLoaded = true;
    console.log('MiddleCrop: Fonts loaded successfully');
  } catch (error) {
    console.error('MiddleCrop: Failed to load fonts:', error);
    throw error;
  }
};

/**
 * Check if fonts are loaded
 * @returns boolean indicating if fonts are loaded
 */
export const areFontsLoaded = (): boolean => {
  return fontsLoaded;
};

/**
 * Get font family name with fallback
 * @param fontName - The font name to get
 * @returns The font family name or system fallback
 */
export const getFontFamily = (fontName: string): string => {
  if (!fontsLoaded) {
    console.warn(`Font ${fontName} requested but fonts not loaded yet. Using system font.`);
    return 'System'; // Fallback to system font
  }

  return fontName;
};

export default {
  loadFonts,
  areFontsLoaded,
  getFontFamily,
};
