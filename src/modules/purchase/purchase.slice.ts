import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  PurchaseState,
  PurchaseProduct,
  PRO_PRODUCT_ID,
  REVENUE_CAT_CONFIG,
} from '../crop/crop.typeDefs';
import { setProStatus } from '../settings/settings.slice';

// Import RevenueCat
let Purchases: any;
try {
  Purchases = require('react-native-purchases').default;
} catch (error) {
  console.warn('RevenueCat not available:', error);
}

// RevenueCat types for better type safety
interface PurchasePackage {
  product: {
    identifier: string;
    priceString: string;
    title: string;
    description?: string;
    currencyCode?: string;
  };
  packageType: string;
  identifier: string;
}

const initialState: PurchaseState = {
  isLoading: false,
  isPro: false,
  products: [],
  error: null,
  lastPurchaseDate: null,
  isRestoring: false,
};

// Async thunks for purchase operations
export const initializePurchases = createAsyncThunk(
  'purchase/initialize',
  async (_, { dispatch, rejectWithValue }) => {
    if (!Purchases) {
      return rejectWithValue('Purchases not available');
    }

    try {
      // Configure RevenueCat with platform-specific API keys
      const apiKey = Platform.select({
        ios: REVENUE_CAT_CONFIG.iOSApiKey,
        android: REVENUE_CAT_CONFIG.androidApiKey,
        default: '',
      });

      if (!apiKey) {
        throw new Error('RevenueCat API key not configured');
      }

      await Purchases.setLogLevel(Purchases.LOG_LEVEL.INFO);
      await Purchases.configure({ apiKey });

      // Check current user entitlements
      const customerInfo = await Purchases.getCustomerInfo();
      const isPro =
        customerInfo.entitlements.active[REVENUE_CAT_CONFIG.entitlementId] !== undefined;

      if (isPro) {
        dispatch(setProStatus(true));
        await AsyncStorage.setItem('isPro', 'true');
      }

      // Get available products
      const offerings = await Purchases.getOfferings();
      const products: PurchaseProduct[] = [];

      if (offerings.current) {
        offerings.current.availablePackages.forEach((pkg: PurchasePackage) => {
          if (pkg.product.identifier === PRO_PRODUCT_ID) {
            products.push({
              identifier: pkg.product.identifier,
              price: pkg.product.priceString,
              title: pkg.product.title,
              description: pkg.product.description || 'MiddleCrop Pro - Unlimited crops and all export formats',
              currencyCode: pkg.product.currencyCode || 'USD',
            });
          }
        });
      }

      return { isPro, products };
    } catch (error) {
      console.error('Purchase initialization failed:', error);
      const message = error instanceof Error ? error.message : 'Purchase initialization failed';
      return rejectWithValue(message);
    }
  },
);

export const purchasePro = createAsyncThunk(
  'purchase/purchasePro',
  async (_, { dispatch, rejectWithValue }) => {
    if (!Purchases) {
      return rejectWithValue('Purchases not available');
    }

    try {
      const offerings = await Purchases.getOfferings();

      if (!offerings.current) {
        throw new Error('No offerings available');
      }

      const proPackage = offerings.current.availablePackages.find(
        (pkg: PurchasePackage) => pkg.product.identifier === PRO_PRODUCT_ID,
      );

      if (!proPackage) {
        throw new Error('Pro package not found');
      }

      // Attempt purchase
      const { customerInfo } = await Purchases.purchasePackage(proPackage);
      const isPro =
        customerInfo.entitlements.active[REVENUE_CAT_CONFIG.entitlementId] !== undefined;

      if (isPro) {
        // Update both purchase and settings state
        dispatch(setProStatus(true));

        const purchaseDate = new Date().toISOString();
        await Promise.all([
          AsyncStorage.setItem('isPro', 'true'),
          AsyncStorage.setItem('lastPurchaseDate', purchaseDate),
        ]);

        return { isPro, lastPurchaseDate: purchaseDate };
      } else {
        throw new Error('Purchase completed but Pro status not activated');
      }
    } catch (error) {
      console.error('Purchase failed:', error);

      // Handle user cancellation gracefully
      if (error && typeof error === 'object' && 'userCancelled' in error) {
        return rejectWithValue('Purchase cancelled by user');
      }

      const message = error instanceof Error ? error.message : 'Purchase failed';
      return rejectWithValue(message);
    }
  },
);

export const restorePurchases = createAsyncThunk(
  'purchase/restore',
  async (_, { dispatch, rejectWithValue }) => {
    if (!Purchases) {
      return rejectWithValue('Purchases not available');
    }

    try {
      const customerInfo = await Purchases.restorePurchases();
      const isPro =
        customerInfo.entitlements.active[REVENUE_CAT_CONFIG.entitlementId] !== undefined;

      if (isPro) {
        dispatch(setProStatus(true));
        await AsyncStorage.setItem('isPro', 'true');
      }

      return { isPro, restored: isPro };
    } catch (error) {
      console.error('Restore purchases failed:', error);
      const message = error instanceof Error ? error.message : 'Restore failed';
      return rejectWithValue(message);
    }
  },
);

export const checkPurchaseStatus = createAsyncThunk(
  'purchase/checkStatus',
  async (_, { dispatch, rejectWithValue }) => {
    if (!Purchases) {
      return rejectWithValue('Purchases not available');
    }

    try {
      const customerInfo = await Purchases.getCustomerInfo();
      const isPro =
        customerInfo.entitlements.active[REVENUE_CAT_CONFIG.entitlementId] !== undefined;

      // Sync with settings state
      dispatch(setProStatus(isPro));
      await AsyncStorage.setItem('isPro', isPro.toString());

      return { isPro };
    } catch (error) {
      console.error('Check purchase status failed:', error);
      const message = error instanceof Error ? error.message : 'Status check failed';
      return rejectWithValue(message);
    }
  },
);

export const loadPersistedPurchaseData = createAsyncThunk(
  'purchase/loadPersisted',
  async (_, { rejectWithValue }) => {
    try {
      const [isPro, lastPurchaseDate] = await Promise.all([
        AsyncStorage.getItem('isPro'),
        AsyncStorage.getItem('lastPurchaseDate'),
      ]);

      return {
        isPro: isPro === 'true',
        lastPurchaseDate,
      };
    } catch (error) {
      console.error('Failed to load persisted purchase data:', error);
      return rejectWithValue('Failed to load purchase data');
    }
  },
);

const purchaseSlice = createSlice({
  name: 'purchase',
  initialState,
  reducers: {
    clearError: state => {
      state.error = null;
    },

    setProducts: (state, action: PayloadAction<PurchaseProduct[]>) => {
      state.products = action.payload;
    },

    updateProStatus: (state, action: PayloadAction<boolean>) => {
      state.isPro = action.payload;
    },

    setLastPurchaseDate: (state, action: PayloadAction<string>) => {
      state.lastPurchaseDate = action.payload;
    },

    resetPurchaseState: state => {
      state.error = null;
      state.isLoading = false;
      state.isRestoring = false;
    },
  },
  extraReducers: builder => {
    builder
      // Initialize purchases
      .addCase(initializePurchases.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializePurchases.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isPro = action.payload.isPro;
        state.products = action.payload.products;
        state.error = null;
      })
      .addCase(initializePurchases.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Purchase Pro
      .addCase(purchasePro.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(purchasePro.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isPro = action.payload.isPro;
        state.lastPurchaseDate = action.payload.lastPurchaseDate;
        state.error = null;
      })
      .addCase(purchasePro.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Restore purchases
      .addCase(restorePurchases.pending, state => {
        state.isRestoring = true;
        state.error = null;
      })
      .addCase(restorePurchases.fulfilled, (state, action) => {
        state.isRestoring = false;
        state.isPro = action.payload.isPro;
        state.error = null;
      })
      .addCase(restorePurchases.rejected, (state, action) => {
        state.isRestoring = false;
        state.error = action.payload as string;
      })

      // Check status
      .addCase(checkPurchaseStatus.pending, state => {
        state.isLoading = true;
      })
      .addCase(checkPurchaseStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isPro = action.payload.isPro;
      })
      .addCase(checkPurchaseStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })

      // Load persisted data
      .addCase(loadPersistedPurchaseData.fulfilled, (state, action) => {
        state.isPro = action.payload.isPro;
        state.lastPurchaseDate = action.payload.lastPurchaseDate;
      })
      .addCase(loadPersistedPurchaseData.rejected, (state, action) => {
        console.warn('Failed to load persisted purchase data:', action.payload);
      });
  },
});

export const { clearError, setProducts, updateProStatus, setLastPurchaseDate, resetPurchaseState } =
  purchaseSlice.actions;

export default purchaseSlice.reducer;

// Enhanced selectors with memoization
const selectPurchaseState = (state: { purchase: PurchaseState }) => state.purchase;

export const selectProProduct = createSelector(
  [selectPurchaseState],
  (purchase) => purchase.products.find(product => product.identifier === PRO_PRODUCT_ID)
);

export const selectCanPurchase = (state: { purchase: PurchaseState }) => {
  return (
    !state.purchase.isLoading &&
    !state.purchase.isPro &&
    state.purchase.products.length > 0 &&
    !state.purchase.isRestoring
  );
};

export const selectPurchaseStatus = createSelector(
  [selectPurchaseState],
  (purchase) => ({
    isPro: purchase.isPro,
    isLoading: purchase.isLoading,
    isRestoring: purchase.isRestoring,
    hasError: !!purchase.error,
    error: purchase.error,
    canPurchase:
      !purchase.isLoading && !purchase.isPro && purchase.products.length > 0,
    canRestore: !purchase.isRestoring && !purchase.isPro,
  })
);

export const selectProductInfo = createSelector(
  [selectProProduct],
  (product) => product
    ? {
        price: product.price,
        title: product.title,
        description: product.description,
        currencyCode: product.currencyCode,
        available: true,
      }
    : {
        price: '$2.99',
        title: 'MiddleCrop Pro',
        description: 'Unlock unlimited crops and all export formats',
        currencyCode: 'USD',
        available: false,
      }
);

export const selectPurchaseHistory = createSelector(
  [selectPurchaseState],
  (purchase) => ({
    isPro: purchase.isPro,
    lastPurchaseDate: purchase.lastPurchaseDate,
    hasPurchaseHistory: !!purchase.lastPurchaseDate,
  })
);
