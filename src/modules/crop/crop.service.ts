import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Skia, ImageFormat } from '@shopify/react-native-skia';
import type { SkImage } from '@shopify/react-native-skia';
import {
  MiddleCropImage,
  CropRegion,
  CropMode,
  ExportFormat,
  Dimensions,
  ErrorType,
  AppError,
  PROCESSING_LIMITS,
  FILE_CONFIG,
} from './crop.typeDefs';

export class HybridCropService {
  static async pickImage(): Promise<MiddleCropImage | null> {
    try {
      const perm = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!perm.granted) {
        throw new Error('Permission to access media library is required');
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
        exif: false,
        allowsMultipleSelection: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) return null;
      const asset = result.assets[0];

      if (
        (asset.width ?? 0) > PROCESSING_LIMITS.maxImageDimension ||
        (asset.height ?? 0) > PROCESSING_LIMITS.maxImageDimension
      ) {
        throw new Error(
          `Image too large. Maximum dimension: ${PROCESSING_LIMITS.maxImageDimension}px`,
        );
      }

      const fileInfo = await FileSystem.getInfoAsync(asset.uri);
      if (
        fileInfo.exists &&
        typeof fileInfo.size === 'number' &&
        fileInfo.size > FILE_CONFIG.maxImageSize
      ) {
        throw new Error(
          `File too large. Maximum size: ${(FILE_CONFIG.maxImageSize / 1024 / 1024).toFixed(1)}MB`,
        );
      }

      return {
        uri: asset.uri,
        dimensions: {
          width: asset.width ?? 0,
          height: asset.height ?? 0,
        },
        fileName: asset.fileName ?? `image_${Date.now()}.jpg`,
        fileSize: fileInfo.exists ? (fileInfo.size ?? 0) : 0,
      };
    } catch (error) {
      throw this.createAppError(ErrorType.IMAGE_LOAD_FAILED, error);
    }
  }

  /** Basic resize operation */
  static async quickResize(uri: string, dimensions: Dimensions): Promise<string> {
    try {
      const { uri: newUri } = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: dimensions.width, height: dimensions.height } }],
        { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG },
      );
      return newUri;
    } catch (error) {
      throw this.createAppError(ErrorType.IMAGE_LOAD_FAILED, error);
    }
  }

  static async quickRotate(uri: string, degrees: number): Promise<string> {
    try {
      const { uri: newUri } = await ImageManipulator.manipulateAsync(uri, [{ rotate: degrees }], {
        compress: 1,
        format: ImageManipulator.SaveFormat.PNG,
      });
      return newUri;
    } catch (error) {
      throw this.createAppError(ErrorType.PROCESSING_FAILED, error);
    }
  }

  static async quickFlip(uri: string, horizontal = false): Promise<string> {
    try {
      const flipType = horizontal
        ? ImageManipulator.FlipType.Horizontal
        : ImageManipulator.FlipType.Vertical;
      const { uri: newUri } = await ImageManipulator.manipulateAsync(uri, [{ flip: flipType }], {
        compress: 1,
        format: ImageManipulator.SaveFormat.PNG,
      });
      return newUri;
    } catch (error) {
      throw this.createAppError(ErrorType.PROCESSING_FAILED, error);
    }
  }

  /** Load image as Skia Image */
  static async loadSkiaImage(uri: string): Promise<SkImage> {
    try {
      const base64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      const data = Skia.Data.fromBase64(base64);
      const image = Skia.Image.MakeImageFromEncoded(data);
      if (!image) {
        throw new Error('Failed to decode image with Skia');
      }
      return image;
    } catch (error) {
      throw this.createAppError(ErrorType.IMAGE_LOAD_FAILED, error);
    }
  }

  /** Perform middle crop using Skia stitching algorithm */
  static async performMiddleCrop(
    sourceImage: SkImage,
    originalDimensions: Dimensions,
    cropRegion: CropRegion,
    onProgress?: (progress: number) => void,
  ): Promise<SkImage> {
    try {
      onProgress?.(10);
      const { x, y, width, height, cropMode } = cropRegion;
      const val = this.validateCropRegion(cropRegion, originalDimensions);
      if (!val.isValid) {
        throw new Error(val.error ?? 'Invalid crop region');
      }

      let newWidth: number, newHeight: number;
      switch (cropMode) {
        case CropMode.HORIZONTAL_STRIP: {
          newWidth = originalDimensions.width;
          newHeight = originalDimensions.height - height;
          break;
        }
        case CropMode.VERTICAL_STRIP: {
          newWidth = originalDimensions.width - width;
          newHeight = originalDimensions.height;
          break;
        }
        case CropMode.RECTANGULAR: {
          newWidth = originalDimensions.width - width;
          newHeight = originalDimensions.height - height;
          break;
        }
        default: {
          throw new Error(`Unsupported crop mode: ${cropMode}`);
        }
      }

      onProgress?.(30);
      const surface = Skia.Surface.Make(newWidth, newHeight);
      if (!surface) {
        throw new Error('Failed to create Skia surface for cropping');
      }

      try {
        const canvas = surface.getCanvas();
        const paint = Skia.Paint();

        onProgress?.(50);

        if (cropMode === CropMode.HORIZONTAL_STRIP) {
          // Top and bottom
          const topRect = Skia.XYWHRect(0, 0, newWidth, y);
          if (topRect.height > 0) {
            canvas.drawImageRect(
              sourceImage,
              topRect,
              Skia.XYWHRect(0, 0, newWidth, topRect.height),
              paint,
            );
          }

          const bottomSourceRect = Skia.XYWHRect(
            0,
            y + height,
            newWidth,
            originalDimensions.height - (y + height),
          );
          if (bottomSourceRect.height > 0) {
            canvas.drawImageRect(
              sourceImage,
              bottomSourceRect,
              Skia.XYWHRect(0, topRect.height, newWidth, bottomSourceRect.height),
              paint,
            );
          }
        } else if (cropMode === CropMode.VERTICAL_STRIP) {
          // Left and right
          const leftRect = Skia.XYWHRect(0, 0, x, newHeight);
          if (leftRect.width > 0) {
            canvas.drawImageRect(
              sourceImage,
              leftRect,
              Skia.XYWHRect(0, 0, leftRect.width, newHeight),
              paint,
            );
          }

          const rightSourceRect = Skia.XYWHRect(
            x + width,
            0,
            originalDimensions.width - (x + width),
            newHeight,
          );
          if (rightSourceRect.width > 0) {
            canvas.drawImageRect(
              sourceImage,
              rightSourceRect,
              Skia.XYWHRect(leftRect.width, 0, rightSourceRect.width, newHeight),
              paint,
            );
          }
        } else if (cropMode === CropMode.RECTANGULAR) {
          // Four quadrants
          onProgress?.(70);

          // Top-left
          const topLeftRect = Skia.XYWHRect(0, 0, x, y);
          if (topLeftRect.width > 0 && topLeftRect.height > 0) {
            canvas.drawImageRect(sourceImage, topLeftRect, Skia.XYWHRect(0, 0, x, y), paint);
          }

          // Top-right
          const topRightSourceRect = Skia.XYWHRect(
            x + width,
            0,
            originalDimensions.width - (x + width),
            y,
          );
          if (topRightSourceRect.width > 0 && topRightSourceRect.height > 0) {
            canvas.drawImageRect(
              sourceImage,
              topRightSourceRect,
              Skia.XYWHRect(x, 0, topRightSourceRect.width, y),
              paint,
            );
          }

          // Bottom-left
          const bottomLeftSourceRect = Skia.XYWHRect(
            0,
            y + height,
            x,
            originalDimensions.height - (y + height),
          );
          if (bottomLeftSourceRect.width > 0 && bottomLeftSourceRect.height > 0) {
            canvas.drawImageRect(
              sourceImage,
              bottomLeftSourceRect,
              Skia.XYWHRect(0, y, x, bottomLeftSourceRect.height),
              paint,
            );
          }

          // Bottom-right
          const bottomRightSourceRect = Skia.XYWHRect(
            x + width,
            y + height,
            originalDimensions.width - (x + width),
            originalDimensions.height - (y + height),
          );
          if (bottomRightSourceRect.width > 0 && bottomRightSourceRect.height > 0) {
            canvas.drawImageRect(
              sourceImage,
              bottomRightSourceRect,
              Skia.XYWHRect(x, y, bottomRightSourceRect.width, bottomRightSourceRect.height),
              paint,
            );
          }
        }

        onProgress?.(90);
        const resultImage = surface.makeImageSnapshot();
        if (!resultImage) {
          throw new Error('Failed to create image snapshot after cropping');
        }

        onProgress?.(100);
        return resultImage;
      } finally {
        surface.dispose();
      }
    } catch (error) {
      throw this.createAppError(ErrorType.PROCESSING_FAILED, error);
    }
  }

  /** Save processed image to device as selected format */
  static async saveProcessedImage(
    image: SkImage,
    format: ExportFormat,
    quality: number,
  ): Promise<string> {
    try {
      const formatMap: Record<ExportFormat, ImageFormat> = {
        [ExportFormat.JPEG]: ImageFormat.JPEG,
        [ExportFormat.PNG]: ImageFormat.PNG,
        [ExportFormat.WEBP]: ImageFormat.WEBP,
      };
      const skiaFormat = formatMap[format];
      const encodedData = image.encodeToBase64(skiaFormat, quality);
      if (!encodedData) {
        throw new Error(`Failed to encode image to ${format} format`);
      }

      const timestamp = Date.now();
      const fileName = `middlecrop_${timestamp}.${format}`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(filePath, encodedData, {
        encoding: FileSystem.EncodingType.Base64,
      });

      return filePath;
    } catch (error) {
      throw this.createAppError(ErrorType.SAVE_FAILED, error);
    }
  }

  /** Share an image file with other apps */
  static async shareImage(uri: string, title?: string): Promise<void> {
    try {
      const available = await Sharing.isAvailableAsync();
      if (!available) {
        throw new Error('Sharing is not available on this device');
      }

      await Sharing.shareAsync(uri, {
        mimeType: 'image/jpeg',
        dialogTitle: title ?? 'Share your cropped image',
      });
    } catch (error) {
      throw this.createAppError(ErrorType.SHARE_FAILED, error);
    }
  }

  /** Delete an image from device storage */
  static async deleteImage(uri: string): Promise<void> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(uri);
      }
    } catch {
      // Safe to ignore delete errors: file might already not exist
    }
  }

  /** Validate crop region for bounds and minimum size */
  static validateCropRegion(
    cropRegion: CropRegion,
    imageDimensions: Dimensions,
  ): { isValid: boolean; error?: string } {
    const { x, y, width, height, cropMode } = cropRegion;
    const { width: imgWidth, height: imgHeight } = imageDimensions;

    if (x < 0 || y < 0 || x + width > imgWidth || y + height > imgHeight) {
      return { isValid: false, error: 'Crop region extends beyond image boundaries' };
    }

    if (width < PROCESSING_LIMITS.minCropSize || height < PROCESSING_LIMITS.minCropSize) {
      return {
        isValid: false,
        error: `Crop size must be at least ${PROCESSING_LIMITS.minCropSize}px`,
      };
    }

    switch (cropMode) {
      case CropMode.HORIZONTAL_STRIP:
        if (x !== 0 || width !== imgWidth)
          return { isValid: false, error: 'Horizontal strip must span full image width' };
        break;
      case CropMode.VERTICAL_STRIP:
        if (y !== 0 || height !== imgHeight)
          return { isValid: false, error: 'Vertical strip must span full image height' };
        break;
      case CropMode.RECTANGULAR:
        break;
    }

    return { isValid: true };
  }

  /** Calculate resulting dimensions after crop */
  static calculateResultDimensions(
    originalDimensions: Dimensions,
    cropRegion: CropRegion,
  ): Dimensions {
    const { width: cropWidth, height: cropHeight, cropMode } = cropRegion;
    const { width: imgWidth, height: imgHeight } = originalDimensions;

    switch (cropMode) {
      case CropMode.HORIZONTAL_STRIP: {
        return { width: imgWidth, height: imgHeight - cropHeight };
      }
      case CropMode.VERTICAL_STRIP: {
        return { width: imgWidth - cropWidth, height: imgHeight };
      }
      case CropMode.RECTANGULAR: {
        return { width: imgWidth - cropWidth, height: imgHeight - cropHeight };
      }
      default: {
        throw new Error(`Unsupported crop mode: ${cropMode}`);
      }
    }
  }

  static async getImageInfo(uri: string): Promise<{
    width: number;
    height: number;
    size: number;
    format?: string;
  }> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error('Image file does not exist');
      }

      // On mobile, use Skia to get dimensions
      const skiaImage = await this.loadSkiaImage(uri);
      return {
        width: skiaImage.width(),
        height: skiaImage.height(),
        size: fileInfo.size ?? 0,
        format: uri.split('.').pop()?.toLowerCase(),
      };
    } catch (error) {
      throw this.createAppError(ErrorType.IMAGE_LOAD_FAILED, error);
    }
  }

  private static createAppError(type: ErrorType, originalError: unknown): Error {
    const message = originalError instanceof Error ? originalError.message : String(originalError);
    const appError: AppError = {
      type,
      message,
      details: originalError instanceof Error ? originalError.stack : undefined,
      timestamp: Date.now(),
      retryable: type !== ErrorType.PERMISSION_DENIED && type !== ErrorType.STORAGE_FULL,
    };
    const error = new Error(message);
    error.name = `AppError:${type}`;
    (error as any).appError = appError;
    return error;
  }
}

export const CropService = HybridCropService;
export default HybridCropService;
