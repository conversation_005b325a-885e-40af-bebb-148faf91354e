import { SkImage } from '@shopify/react-native-skia';

// Core geometry types
export interface Dimensions {
  readonly width: number;
  readonly height: number;
}

export interface CropRegion {
  readonly x: number;
  readonly y: number;
  readonly width: number;
  readonly height: number;
  readonly cropMode: CropMode;
}

export enum CropMode {
  HORIZONTAL_STRIP = 'horizontal_strip',
  VERTICAL_STRIP = 'vertical_strip',
  RECTANGULAR = 'rectangular',
}

export enum ExportFormat {
  JPEG = 'jpeg',
  PNG = 'png',
  WEBP = 'webp',
}

export interface MiddleCropImage {
  readonly uri: string;
  readonly dimensions: Dimensions;
  readonly fileName: string;
  readonly fileSize: number;
  readonly skiaImage?: SkImage;
}

export interface CropHistoryEntry {
  readonly id: string;
  readonly originalImageUri: string;
  readonly resultImageUri: string;
  readonly timestamp: number;
  readonly cropRegion: CropRegion;
  readonly format: ExportFormat;
  readonly quality: number;
  readonly originalDimensions: Dimensions;
  readonly resultDimensions: Dimensions;
  readonly processingTimeMs?: number;
}

export interface SocialPreset {
  readonly name: string;
  readonly dimensions: Dimensions;
  readonly description: string;
  readonly category: 'instagram' | 'tiktok' | 'facebook' | 'twitter' | 'linkedin';
  readonly aspectRatio: number;
}

// Redux state interfaces
export interface CropState {
  selectedImage: MiddleCropImage | null;
  cropRegion: CropRegion | null;
  cropMode: CropMode;
  isProcessing: boolean;
  processingProgress: number;
  cropHistory: CropHistoryEntry[];
  cropsToday: number;
  lastCropDate: string;
  error: string | null;
}

export interface SettingsState {
  isPro: boolean;
  exportFormat: ExportFormat;
  quality: number;
  hapticFeedback: boolean;
  autoSaveToGallery: boolean;
  theme: 'light' | 'dark' | 'system';
  showOnboarding: boolean;
  dailyCropLimit: number;
  historyLimit: number;
}

export interface PurchaseState {
  isLoading: boolean;
  isPro: boolean;
  products: PurchaseProduct[];
  error: string | null;
  lastPurchaseDate: string | null;
  isRestoring: boolean;
}

export interface PurchaseProduct {
  identifier: string;
  price: string;
  title: string;
  description: string;
  currencyCode: string;
}

// App constants
export const FREE_DAILY_LIMIT = 5;
export const FREE_HISTORY_LIMIT = 10;
export const PRO_PRICE = '$2.99';
export const PRO_PRODUCT_ID = 'middlecrop_pro_299';

// Social media presets optimized for 2025
export const SOCIAL_PRESETS: readonly SocialPreset[] = [
  {
    name: 'Instagram Story',
    dimensions: { width: 1080, height: 1920 },
    description: '9:16 vertical',
    category: 'instagram',
    aspectRatio: 9 / 16,
  },
  {
    name: 'Instagram Post',
    dimensions: { width: 1080, height: 1080 },
    description: '1:1 square',
    category: 'instagram',
    aspectRatio: 1,
  },
  {
    name: 'Instagram Reel',
    dimensions: { width: 1080, height: 1920 },
    description: '9:16 vertical',
    category: 'instagram',
    aspectRatio: 9 / 16,
  },
  {
    name: 'TikTok Video',
    dimensions: { width: 1080, height: 1920 },
    description: '9:16 vertical',
    category: 'tiktok',
    aspectRatio: 9 / 16,
  },
  {
    name: 'Facebook Cover',
    dimensions: { width: 820, height: 312 },
    description: '2.6:1 landscape',
    category: 'facebook',
    aspectRatio: 820 / 312,
  },
  {
    name: 'Facebook Post',
    dimensions: { width: 1200, height: 630 },
    description: '1.9:1 landscape',
    category: 'facebook',
    aspectRatio: 1200 / 630,
  },
  {
    name: 'Twitter Header',
    dimensions: { width: 1500, height: 500 },
    description: '3:1 landscape',
    category: 'twitter',
    aspectRatio: 3,
  },
  {
    name: 'Twitter Post',
    dimensions: { width: 1200, height: 675 },
    description: '16:9 landscape',
    category: 'twitter',
    aspectRatio: 16 / 9,
  },
  {
    name: 'LinkedIn Banner',
    dimensions: { width: 1584, height: 396 },
    description: '4:1 landscape',
    category: 'linkedin',
    aspectRatio: 4,
  },
  {
    name: 'LinkedIn Post',
    dimensions: { width: 1200, height: 627 },
    description: '1.9:1 landscape',
    category: 'linkedin',
    aspectRatio: 1200 / 627,
  },
] as const;

export const QUALITY_PRESETS = {
  LOW: 60,
  MEDIUM: 75,
  HIGH: 90,
  MAXIMUM: 100,
} as const;

export const FORMAT_QUALITY_DEFAULTS = {
  [ExportFormat.JPEG]: 90,
  [ExportFormat.PNG]: 100,
  [ExportFormat.WEBP]: 90,
} as const;

// App configuration
export const APP_CONFIG = {
  name: 'MiddleCrop',
  proName: 'MiddleCrop Pro',
  organization: 'CapitalClientsMedia',
  bundleId: 'com.capitalclientsmedia.middlecrop',
  supportEmail: '<EMAIL>',
  privacyUrl: 'https://capitalclientsmedia.com/middlecrop/privacy',
  termsUrl: 'https://capitalclientsmedia.com/middlecrop/terms',
  version: '1.0.0',
  buildNumber: 1,
} as const;

// Revenue Cat configuration
export const REVENUE_CAT_CONFIG = {
  entitlementId: 'pro_features',
  iOSApiKey: process.env.EXPO_PUBLIC_REVENUECAT_IOS_API_KEY ?? '',
  androidApiKey: process.env.EXPO_PUBLIC_REVENUECAT_ANDROID_API_KEY ?? '',
} as const;

// File system constants
export const FILE_CONFIG = {
  maxImageSize: 50 * 1024 * 1024, // 50MB
  supportedFormats: ['jpg', 'jpeg', 'png', 'webp', 'heic'],
  thumbnailSize: { width: 300, height: 300 },
  previewSize: { width: 1080, height: 1080 },
} as const;

// Processing limits
export const PROCESSING_LIMITS = {
  minCropSize: 50,
  maxImageDimension: 8192,
  timeoutMs: 30000,
  maxConcurrentOperations: 3,
} as const;

// Error types
export enum ErrorType {
  IMAGE_LOAD_FAILED = 'image_load_failed',
  CROP_VALIDATION_FAILED = 'crop_validation_failed',
  PROCESSING_FAILED = 'processing_failed',
  SAVE_FAILED = 'save_failed',
  SHARE_FAILED = 'share_failed',
  PURCHASE_FAILED = 'purchase_failed',
  PERMISSION_DENIED = 'permission_denied',
  NETWORK_ERROR = 'network_error',
  STORAGE_FULL = 'storage_full',
  UNKNOWN_ERROR = 'unknown_error',
}

export interface AppError {
  type: ErrorType;
  message: string;
  details?: string;
  timestamp: number;
  retryable: boolean;
}

// Navigation types for React Navigation integration
export type RootStackParamList = {
  Main: undefined;
  Onboarding: undefined;
  ProUpgrade: undefined;
};

export type TabParamList = {
  Home: undefined;
  History: undefined;
  Settings: undefined;
};

export type HomeStackParamList = {
  CropScreen: undefined;
  PreviewScreen: {
    imageUri: string;
    cropRegion: CropRegion;
  };
  ExportScreen: {
    processedImageUri: string;
    originalImage: MiddleCropImage;
    cropRegion: CropRegion;
  };
};

export type HistoryStackParamList = {
  HistoryList: undefined;
  HistoryDetail: {
    entryId: string;
  };
};

export type SettingsStackParamList = {
  SettingsMain: undefined;
  SettingsExport: undefined;
  SettingsAbout: undefined;
  SettingsSupport: undefined;
};
