import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  CropState,
  MiddleCropImage,
  CropRegion,
  CropMode,
  CropHistoryEntry,
  FREE_DAILY_LIMIT,
  FREE_HISTORY_LIMIT,
  ExportFormat,
  AppError,
} from './crop.typeDefs';

const initialState: CropState = {
  selectedImage: null,
  cropRegion: null,
  cropMode: CropMode.HORIZONTAL_STRIP,
  isProcessing: false,
  processingProgress: 0,
  cropHistory: [],
  cropsToday: 0,
  lastCropDate: new Date().toISOString().split('T')[0],
  error: null,
};

// Async thunks for persistence
export const loadPersistedCropData = createAsyncThunk(
  'crop/loadPersistedData',
  async (_, { rejectWithValue }) => {
    try {
      const [historyJson, cropsToday, lastCropDate] = await Promise.all([
        AsyncStorage.getItem('cropHistory'),
        AsyncStorage.getItem('cropsToday'),
        AsyncStorage.getItem('lastCropDate'),
      ]);

      return {
        history: historyJson ? JSON.parse(historyJson) : [],
        cropsToday: cropsToday ? parseInt(cropsToday, 10) : 0,
        lastCropDate: lastCropDate ?? new Date().toISOString().split('T')[0],
      };
    } catch (error) {
      console.error('Failed to load persisted crop data:', error);
      return rejectWithValue('Failed to load saved data');
    }
  },
);

export const persistCropHistory = createAsyncThunk(
  'crop/persistHistory',
  async (history: CropHistoryEntry[], { rejectWithValue }) => {
    try {
      await AsyncStorage.setItem('cropHistory', JSON.stringify(history));
      return history;
    } catch (error) {
      console.error('Failed to persist crop history:', error);
      return rejectWithValue('Failed to save history');
    }
  },
);

export const persistDailyCount = createAsyncThunk(
  'crop/persistDailyCount',
  async ({ count, date }: { count: number; date: string }, { rejectWithValue }) => {
    try {
      await Promise.all([
        AsyncStorage.setItem('cropsToday', count.toString()),
        AsyncStorage.setItem('lastCropDate', date),
      ]);
      return { count, date };
    } catch (error) {
      console.error('Failed to persist daily count:', error);
      return rejectWithValue('Failed to save daily count');
    }
  },
);

const cropSlice = createSlice({
  name: 'crop',
  initialState,
  reducers: {
    setSelectedImage: (state, action: PayloadAction<MiddleCropImage | null>) => {
      state.selectedImage = action.payload;
      state.cropRegion = null;
      state.error = null;
    },

    setCropRegion: (state, action: PayloadAction<CropRegion | null>) => {
      state.cropRegion = action.payload;
      state.error = null;
    },

    setCropMode: (state, action: PayloadAction<CropMode>) => {
      state.cropMode = action.payload;
      // Reset crop region when mode changes
      state.cropRegion = null;
    },

    setProcessing: (state, action: PayloadAction<boolean>) => {
      state.isProcessing = action.payload;
      if (!action.payload) {
        state.processingProgress = 0;
      }
    },

    setProcessingProgress: (state, action: PayloadAction<number>) => {
      state.processingProgress = Math.max(0, Math.min(100, action.payload));
    },

    addCropToHistory: (state, action: PayloadAction<CropHistoryEntry>) => {
      // Add to beginning of history
      state.cropHistory.unshift(action.payload);

      // Apply free tier history limit
      if (state.cropHistory.length > FREE_HISTORY_LIMIT) {
        state.cropHistory = state.cropHistory.slice(0, FREE_HISTORY_LIMIT);
      }

      // Update daily crop count
      const today = new Date().toISOString().split('T')[0];
      if (state.lastCropDate !== today) {
        state.cropsToday = 1;
        state.lastCropDate = today;
      } else {
        state.cropsToday += 1;
      }

      // Clear processing state
      state.isProcessing = false;
      state.processingProgress = 0;
    },

    removeCropFromHistory: (state, action: PayloadAction<string>) => {
      state.cropHistory = state.cropHistory.filter(entry => entry.id !== action.payload);
    },

    clearCropHistory: state => {
      state.cropHistory = [];
    },

    resetDailyCount: state => {
      const today = new Date().toISOString().split('T')[0];
      if (state.lastCropDate !== today) {
        state.cropsToday = 0;
        state.lastCropDate = today;
      }
    },

    setError: (state, action: PayloadAction<AppError | null>) => {
      state.error = action.payload?.message ?? null;
      state.isProcessing = false;
      state.processingProgress = 0;
    },

    clearError: state => {
      state.error = null;
    },

    resetCropState: state => {
      state.selectedImage = null;
      state.cropRegion = null;
      state.isProcessing = false;
      state.processingProgress = 0;
      state.error = null;
    },

    updateHistoryEntry: (
      state,
      action: PayloadAction<{ id: string; updates: Partial<CropHistoryEntry> }>,
    ) => {
      const { id, updates } = action.payload;
      const index = state.cropHistory.findIndex(entry => entry.id === id);
      if (index !== -1) {
        state.cropHistory[index] = { ...state.cropHistory[index], ...updates };
      }
    },
  },
  extraReducers: builder => {
    builder
      .addCase(loadPersistedCropData.fulfilled, (state, action) => {
        state.cropHistory = action.payload.history;
        state.cropsToday = action.payload.cropsToday;
        state.lastCropDate = action.payload.lastCropDate;
      })
      .addCase(loadPersistedCropData.rejected, (state, action) => {
        console.warn('Failed to load persisted data:', action.payload);
      })
      .addCase(persistCropHistory.rejected, (state, action) => {
        console.warn('Failed to persist history:', action.payload);
      })
      .addCase(persistDailyCount.rejected, (state, action) => {
        console.warn('Failed to persist daily count:', action.payload);
      });
  },
});

export const {
  setSelectedImage,
  setCropRegion,
  setCropMode,
  setProcessing,
  setProcessingProgress,
  addCropToHistory,
  removeCropFromHistory,
  clearCropHistory,
  resetDailyCount,
  setError,
  clearError,
  resetCropState,
  updateHistoryEntry,
} = cropSlice.actions;

export default cropSlice.reducer;

// Enhanced selectors
export const selectCanCrop = (state: { crop: CropState; settings: { isPro: boolean } }) => {
  const { crop, settings } = state;
  return !!(
    crop.selectedImage &&
    crop.cropRegion &&
    !crop.isProcessing &&
    (settings.isPro || crop.cropsToday < FREE_DAILY_LIMIT)
  );
};

export const selectRemainingCrops = (state: { crop: CropState; settings: { isPro: boolean } }) => {
  const { crop, settings } = state;
  if (settings.isPro) return Infinity;
  return Math.max(0, FREE_DAILY_LIMIT - crop.cropsToday);
};

export const selectHistoryByFormat = (state: { crop: CropState }, format: ExportFormat) => {
  return state.crop.cropHistory.filter(entry => entry.format === format);
};

export const selectRecentHistory = (state: { crop: CropState }, limit: number = 5) => {
  return state.crop.cropHistory.slice(0, limit);
};

export const selectHistoryStats = (state: { crop: CropState }) => {
  const history = state.crop.cropHistory;
  return {
    totalCrops: history.length,
    averageProcessingTime:
      history.length > 0
        ? history.reduce((acc, entry) => acc + (entry.processingTimeMs ?? 0), 0) / history.length
        : 0,
    formatDistribution: history.reduce(
      (acc, entry) => {
        acc[entry.format] = (acc[entry.format] ?? 0) + 1;
        return acc;
      },
      {} as Record<ExportFormat, number>,
    ),
    modeDistribution: history.reduce(
      (acc, entry) => {
        acc[entry.cropRegion.cropMode] = (acc[entry.cropRegion.cropMode] || 0) + 1;
        return acc;
      },
      {} as Record<CropMode, number>,
    ),
  };
};

export const selectNeedsDailyReset = (state: { crop: CropState }) => {
  const today = new Date().toISOString().split('T')[0];
  return state.crop.lastCropDate !== today;
};
