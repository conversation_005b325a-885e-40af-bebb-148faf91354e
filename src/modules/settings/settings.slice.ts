import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  SettingsState,
  ExportFormat,
  FREE_DAILY_LIMIT,
  FREE_HISTORY_LIMIT,
  FORMAT_QUALITY_DEFAULTS,
} from '../crop/crop.typeDefs';

const initialState: SettingsState = {
  isPro: false,
  exportFormat: ExportFormat.JPEG,
  quality: FORMAT_QUALITY_DEFAULTS[ExportFormat.JPEG],
  hapticFeedback: true,
  autoSaveToGallery: false,
  theme: 'system',
  showOnboarding: true,
  dailyCropLimit: FREE_DAILY_LIMIT,
  historyLimit: FREE_HISTORY_LIMIT,
};

// Async thunks for settings persistence
export const loadPersistedSettings = createAsyncThunk(
  'settings/loadPersisted',
  async (_, { rejectWithValue }) => {
    try {
      const keys = [
        'isPro',
        'exportFormat',
        'quality',
        'hapticFeedback',
        'autoSaveToGallery',
        'theme',
        'showOnboarding',
      ];

      const values = await AsyncStorage.multiGet(keys);
      const settings: Partial<SettingsState> = {};

      values.forEach(([key, value]) => {
        if (value !== null) {
          switch (key) {
            case 'isPro':
            case 'hapticFeedback':
            case 'autoSaveToGallery':
            case 'showOnboarding':
              (settings[key as keyof SettingsState] as boolean) = value === 'true';
              break;
            case 'quality':
              settings.quality = parseInt(value, 10);
              break;
            case 'exportFormat':
              settings.exportFormat = value as ExportFormat;
              break;
            case 'theme':
              settings.theme = value as 'light' | 'dark' | 'system';
              break;
          }
        }
      });

      return settings;
    } catch (error) {
      console.error('Failed to load persisted settings:', error);
      return rejectWithValue('Failed to load saved settings');
    }
  },
);

export const persistSetting = createAsyncThunk(
  'settings/persistSetting',
  async ({ key, value }: { key: string; value: any }, { rejectWithValue }) => {
    try {
      await AsyncStorage.setItem(key, String(value));
      return { key, value };
    } catch (error) {
      console.error(`Failed to persist setting ${key}:`, error);
      return rejectWithValue(`Failed to save ${key} setting`);
    }
  },
);

export const clearAllSettings = createAsyncThunk(
  'settings/clearAll',
  async (_, { rejectWithValue }) => {
    try {
      const keys = [
        'isPro',
        'exportFormat',
        'quality',
        'hapticFeedback',
        'autoSaveToGallery',
        'theme',
        'showOnboarding',
      ];

      await AsyncStorage.multiRemove(keys);
      return initialState;
    } catch (error) {
      console.error('Failed to clear settings:', error);
      return rejectWithValue('Failed to clear settings');
    }
  },
);

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setProStatus: (state, action: PayloadAction<boolean>) => {
      state.isPro = action.payload;

      if (action.payload) {
        // Pro features unlocked
        state.dailyCropLimit = Infinity;
        state.historyLimit = Infinity;
      } else {
        // Revert to free tier limits
        state.dailyCropLimit = FREE_DAILY_LIMIT;
        state.historyLimit = FREE_HISTORY_LIMIT;

        // Reset to free tier format if using Pro-only format
        if (state.exportFormat !== ExportFormat.JPEG) {
          state.exportFormat = ExportFormat.JPEG;
          state.quality = FORMAT_QUALITY_DEFAULTS[ExportFormat.JPEG];
        }
      }
    },

    setExportFormat: (state, action: PayloadAction<ExportFormat>) => {
      // Only allow non-JPEG formats for Pro users
      if (!state.isPro && action.payload !== ExportFormat.JPEG) {
        return; // Reject the action silently
      }

      state.exportFormat = action.payload;
      state.quality = FORMAT_QUALITY_DEFAULTS[action.payload];
    },

    setQuality: (state, action: PayloadAction<number>) => {
      // Only Pro users can adjust quality for JPEG/WEBP
      if (!state.isPro && state.exportFormat !== ExportFormat.PNG) {
        return; // Reject the action silently
      }

      const quality = Math.max(10, Math.min(100, action.payload));
      state.quality = quality;
    },

    setHapticFeedback: (state, action: PayloadAction<boolean>) => {
      state.hapticFeedback = action.payload;
    },

    setAutoSaveToGallery: (state, action: PayloadAction<boolean>) => {
      state.autoSaveToGallery = action.payload;
    },

    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },

    setShowOnboarding: (state, action: PayloadAction<boolean>) => {
      state.showOnboarding = action.payload;
    },

    resetToDefaults: state => {
      // Keep Pro status but reset other settings
      const isPro = state.isPro;
      Object.assign(state, initialState);
      state.isPro = isPro;

      if (isPro) {
        state.dailyCropLimit = Infinity;
        state.historyLimit = Infinity;
      }
    },

    bulkUpdateSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      Object.assign(state, action.payload);
    },
  },
  extraReducers: builder => {
    builder
      .addCase(loadPersistedSettings.fulfilled, (state, action) => {
        Object.assign(state, action.payload);

        // Ensure Pro limits are properly set
        if (state.isPro) {
          state.dailyCropLimit = Infinity;
          state.historyLimit = Infinity;
        }
      })
      .addCase(loadPersistedSettings.rejected, (state, action) => {
        console.warn('Failed to load persisted settings:', action.payload);
      })
      .addCase(clearAllSettings.fulfilled, (state, action) => {
        Object.assign(state, action.payload);
      })
      .addCase(clearAllSettings.rejected, (state, action) => {
        console.warn('Failed to clear settings:', action.payload);
      });
  },
});

export const {
  setProStatus,
  setExportFormat,
  setQuality,
  setHapticFeedback,
  setAutoSaveToGallery,
  setTheme,
  setShowOnboarding,
  resetToDefaults,
  bulkUpdateSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;

// Enhanced selectors
export const selectAvailableFormats = (state: { settings: SettingsState }) => {
  if (state.settings.isPro) {
    return [ExportFormat.JPEG, ExportFormat.PNG, ExportFormat.WEBP];
  }
  return [ExportFormat.JPEG];
};

export const selectCanAdjustQuality = (state: { settings: SettingsState }) => {
  return (
    state.settings.isPro &&
    (state.settings.exportFormat === ExportFormat.JPEG ||
      state.settings.exportFormat === ExportFormat.WEBP)
  );
};

export const selectThemeConfig = (state: { settings: SettingsState }) => {
  const { theme } = state.settings;
  return {
    theme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
    isSystem: theme === 'system',
  };
};

export const selectExportConfig = (state: { settings: SettingsState }) => {
  const { exportFormat, quality, isPro } = state.settings;
  return {
    format: exportFormat,
    quality,
    canChangeFormat: isPro,
    canAdjustQuality:
      isPro && (exportFormat === ExportFormat.JPEG || exportFormat === ExportFormat.WEBP),
    availableFormats: isPro ? Object.values(ExportFormat) : [ExportFormat.JPEG],
  };
};

export const selectUserTier = (state: { settings: SettingsState }) => {
  const { isPro, dailyCropLimit, historyLimit } = state.settings;
  return {
    isPro,
    tier: isPro ? 'pro' : 'free',
    dailyLimit: dailyCropLimit,
    historyLimit,
    hasUnlimitedCrops: isPro,
    hasUnlimitedHistory: isPro,
  };
};
