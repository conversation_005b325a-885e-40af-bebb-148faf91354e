import React, { useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Image,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';

import Button from '../../components/Button';
import { useAppSelector, useAppDispatch } from '../../utils/store';
import { HybridCropService } from '../../modules/crop/crop.service';
import {
  removeCropFromHistory,
  clearCropHistory,
  selectHistoryStats,
  selectRecentHistory,
} from '../../modules/crop/crop.slice';
import { CropHistoryEntry, CropMode, ExportFormat } from '../../modules/crop/crop.typeDefs';
import { colors, fonts } from '../../theme';

export default function History() {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  const cropHistory = useAppSelector(state => state.crop.cropHistory);
  const isPro = useAppSelector(state => state.settings.isPro);
  const hapticFeedback = useAppSelector(state => state.settings.hapticFeedback);
  const historyStats = useAppSelector(selectHistoryStats);

  // Share image handler
  const handleShare = useCallback(
    async (entry: CropHistoryEntry) => {
      try {
        if (hapticFeedback) {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }

        await HybridCropService.shareImage(
          entry.resultImageUri,
          `MiddleCrop - ${entry.format.toUpperCase()}`,
        );
      } catch (error) {
        console.error('Failed to share image:', error);
        Alert.alert('Error', 'Failed to share image');
      }
    },
    [hapticFeedback],
  );

  // Delete image handler
  const handleDelete = useCallback(
    (entry: CropHistoryEntry) => {
      Alert.alert('Delete Crop', 'Are you sure you want to delete this cropped image?', [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            if (hapticFeedback) {
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            }

            // Delete file from device
            await HybridCropService.deleteImage(entry.resultImageUri);

            // Remove from history
            dispatch(removeCropFromHistory(entry.id));
          },
        },
      ]);
    },
    [dispatch, hapticFeedback],
  );

  // Clear all history handler
  const handleClearAll = useCallback(() => {
    Alert.alert(
      'Clear All History',
      `Are you sure you want to delete all ${cropHistory.length} cropped images?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            if (hapticFeedback) {
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            }

            // Delete all files
            await Promise.all(
              cropHistory.map(entry => HybridCropService.deleteImage(entry.resultImageUri)),
            );

            // Clear history
            dispatch(clearCropHistory());
          },
        },
      ],
    );
  }, [cropHistory, dispatch, hapticFeedback]);

  // Format crop mode for display
  const formatCropMode = useCallback((mode: CropMode) => {
    switch (mode) {
      case CropMode.HORIZONTAL_STRIP:
        return 'Horizontal';
      case CropMode.VERTICAL_STRIP:
        return 'Vertical';
      case CropMode.RECTANGULAR:
        return 'Rectangle';
      default:
        return 'Unknown';
    }
  }, []);

  // Format time ago
  const formatTimeAgo = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
  }, []);

  // Render history item
  const renderHistoryItem = useCallback(
    ({ item }: { item: CropHistoryEntry }) => {
      return (
        <View style={styles.historyItem}>
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: item.resultImageUri }}
              style={styles.thumbnail}
              resizeMode="cover"
            />
            <View style={styles.imageOverlay}>
              <Text style={styles.formatBadge}>{item.format.toUpperCase()}</Text>
            </View>
          </View>

          <View style={styles.itemInfo}>
            <Text style={styles.itemTitle} numberOfLines={1}>
              {formatCropMode(item.cropRegion.cropMode)} Crop
            </Text>
            <Text style={styles.itemSubtitle} numberOfLines={1}>
              {item.resultDimensions.width}×{item.resultDimensions.height}
            </Text>
            <Text style={styles.itemTime}>{formatTimeAgo(item.timestamp)}</Text>
            <Text style={styles.itemQuality}>Quality: {item.quality}%</Text>
          </View>

          <View style={styles.itemActions}>
            <TouchableOpacity style={styles.actionButton} onPress={() => handleShare(item)}>
              <Text style={styles.actionButtonText}>📤</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton]}
              onPress={() => handleDelete(item)}>
              <Text style={styles.actionButtonText}>🗑️</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    },
    [formatCropMode, formatTimeAgo, handleShare, handleDelete],
  );

  // Stats component
  const StatsComponent = useMemo(() => {
    if (cropHistory.length === 0) return null;

    return (
      <View style={styles.statsContainer}>
        <Text style={styles.statsTitle}>Your Crop Statistics</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{historyStats.totalCrops}</Text>
            <Text style={styles.statLabel}>Total Crops</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{Math.round(historyStats.averageProcessingTime)}ms</Text>
            <Text style={styles.statLabel}>Avg. Time</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Object.keys(historyStats.formatDistribution).length}
            </Text>
            <Text style={styles.statLabel}>Formats Used</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Object.keys(historyStats.modeDistribution).length}
            </Text>
            <Text style={styles.statLabel}>Crop Modes</Text>
          </View>
        </View>
      </View>
    );
  }, [cropHistory.length, historyStats]);

  // Empty state
  if (cropHistory.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Crop History</Text>
          <Text style={styles.subtitle}>Your cropped images appear here</Text>
        </View>

        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>📱</Text>
          <Text style={styles.emptyStateTitle}>No Crops Yet</Text>
          <Text style={styles.emptyStateSubtitle}>
            Your cropped images will appear here after you create them
          </Text>
          <Button
            title="Start Cropping"
            onPress={() => navigation.navigate('Home' as never)}
            style={styles.emptyStateButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Crop History</Text>
        <Text style={styles.subtitle}>
          {cropHistory.length} {cropHistory.length === 1 ? 'crop' : 'crops'}
          {!isPro && ' (Free: 10 max)'}
        </Text>
      </View>

      {StatsComponent}

      <FlatList
        data={cropHistory}
        renderItem={renderHistoryItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />

      {cropHistory.length > 0 && (
        <View style={styles.actions}>
          <Button title="Clear All History" onPress={handleClearAll} style={styles.clearButton} />

          {!isPro && cropHistory.length >= 10 && (
            <TouchableOpacity
              style={styles.upgradePrompt}
              onPress={() => navigation.navigate('ProUpgrade' as never)}>
              <Text style={styles.upgradeText}>
                ⚠️ History limited to 10 items. Upgrade to Pro for unlimited history!
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.surface,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.text,
    fontFamily: fonts.bold,
  },
  subtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 4,
    fontFamily: fonts.regular,
  },
  statsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.surface,
    marginBottom: 8,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
    textAlign: 'center',
    fontFamily: fonts.semibold,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    fontFamily: fonts.bold,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  historyItem: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  separator: {
    height: 12,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: colors.disabled,
  },
  imageOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  formatBadge: {
    backgroundColor: colors.primary,
    color: 'white',
    fontSize: 8,
    fontWeight: '600',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    fontFamily: fonts.semibold,
  },
  itemInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    fontFamily: fonts.semibold,
  },
  itemSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  itemTime: {
    fontSize: 12,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  itemQuality: {
    fontSize: 12,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  itemActions: {
    justifyContent: 'space-between',
    marginLeft: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
  },
  deleteButton: {
    backgroundColor: colors.error,
  },
  actionButtonText: {
    fontSize: 16,
  },
  actions: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.surface,
  },
  clearButton: {
    backgroundColor: colors.error,
    borderRadius: 12,
    paddingVertical: 16,
  },
  clearButtonText: {
    color: 'white',
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
  upgradePrompt: {
    marginTop: 12,
    padding: 12,
    backgroundColor: colors.warning + '20',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.warning,
  },
  upgradeText: {
    fontSize: 14,
    color: colors.warning,
    textAlign: 'center',
    fontWeight: '500',
    fontFamily: fonts.medium,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
    fontFamily: fonts.semibold,
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
    fontFamily: fonts.regular,
  },
  emptyStateButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 32,
    borderRadius: 12,
  },
});
