import React, { useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  Linking,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';

import Button from '../../components/Button';
import GradientButton from '../../components/GradientButton';
import { useAppSelector, useAppDispatch } from '../../utils/store';
import {
  setExportFormat,
  setQuality,
  setHapticFeedback,
  setAutoSaveToGallery,
  setTheme,
  selectAvailableFormats,
  selectCanAdjustQuality,
  selectExportConfig,
  selectUserTier,
} from '../../modules/settings/settings.slice';
import {
  purchasePro,
  restorePurchases,
  selectPurchaseStatus,
  selectProductInfo,
} from '../../modules/purchase/purchase.slice';
import { ExportFormat, QUALITY_PRESETS, APP_CONFIG } from '../../modules/crop/crop.typeDefs';
import { colors, fonts } from '../../theme';

export default function Settings() {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  // Redux state
  const settings = useAppSelector(state => state.settings);
  const availableFormats = useAppSelector(selectAvailableFormats);
  const canAdjustQuality = useAppSelector(selectCanAdjustQuality);
  const exportConfig = useAppSelector(selectExportConfig);
  const userTier = useAppSelector(selectUserTier);
  const purchaseStatus = useAppSelector(selectPurchaseStatus);
  const productInfo = useAppSelector(selectProductInfo);

  // Pro upgrade handler
  const handleUpgradeToPro = useCallback(async () => {
    try {
      if (settings.hapticFeedback) {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      await dispatch(purchasePro()).unwrap();

      Alert.alert(
        'Welcome to MiddleCrop Pro!',
        'You now have unlimited crops and access to all export formats.',
        [{ text: 'Awesome!', style: 'default' }],
      );
    } catch (error) {
      console.error('Purchase failed:', error);
      Alert.alert(
        'Purchase Failed',
        error instanceof Error ? error.message : 'Unable to complete purchase',
      );
    }
  }, [dispatch, settings.hapticFeedback]);

  // Restore purchases handler
  const handleRestorePurchases = useCallback(async () => {
    try {
      if (settings.hapticFeedback) {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      const result = await dispatch(restorePurchases()).unwrap();

      if (result.isPro) {
        Alert.alert('Success', 'Your Pro purchase has been restored!');
      } else {
        Alert.alert('No Purchases Found', 'No previous purchases were found to restore.');
      }
    } catch (error) {
      console.error('Restore failed:', error);
      Alert.alert('Restore Failed', 'Unable to restore purchases');
    }
  }, [dispatch, settings.hapticFeedback]);

  // Format selection handler
  const handleFormatChange = useCallback(
    (format: ExportFormat) => {
      if (settings.hapticFeedback) {
        Haptics.selectionAsync();
      }
      dispatch(setExportFormat(format));
    },
    [dispatch, settings.hapticFeedback],
  );

  // Quality change handler
  const handleQualityChange = useCallback(
    (quality: number) => {
      if (settings.hapticFeedback) {
        Haptics.selectionAsync();
      }
      dispatch(setQuality(quality));
    },
    [dispatch, settings.hapticFeedback],
  );

  // Theme change handler
  const handleThemeChange = useCallback(
    (theme: 'light' | 'dark' | 'system') => {
      if (settings.hapticFeedback) {
        Haptics.selectionAsync();
      }
      dispatch(setTheme(theme));
    },
    [dispatch, settings.hapticFeedback],
  );

  // Contact support handler
  const handleContactSupport = useCallback(async () => {
    const emailUrl = `mailto:${APP_CONFIG.supportEmail}?subject=MiddleCrop Support&body=Hi MiddleCrop team,%0D%0A%0D%0A`;

    try {
      const canOpen = await Linking.canOpenURL(emailUrl);
      if (canOpen) {
        await Linking.openURL(emailUrl);
      } else {
        Alert.alert('Email Support', `Please contact us at:\n${APP_CONFIG.supportEmail}`, [
          { text: 'OK' },
        ]);
      }
    } catch (error) {
      Alert.alert('Email Support', `Please contact us at:\n${APP_CONFIG.supportEmail}`, [
        { text: 'OK' },
      ]);
    }
  }, []);

  // Privacy policy handler
  const handlePrivacyPolicy = useCallback(async () => {
    try {
      const canOpen = await Linking.canOpenURL(APP_CONFIG.privacyUrl);
      if (canOpen) {
        await Linking.openURL(APP_CONFIG.privacyUrl);
      } else {
        Alert.alert('Privacy Policy', 'Unable to open privacy policy');
      }
    } catch (error) {
      Alert.alert('Privacy Policy', 'Unable to open privacy policy');
    }
  }, []);

  // Terms handler
  const handleTerms = useCallback(async () => {
    try {
      const canOpen = await Linking.canOpenURL(APP_CONFIG.termsUrl);
      if (canOpen) {
        await Linking.openURL(APP_CONFIG.termsUrl);
      } else {
        Alert.alert('Terms of Service', 'Unable to open terms of service');
      }
    } catch (error) {
      Alert.alert('Terms of Service', 'Unable to open terms of service');
    }
  }, []);

  // Pro upgrade section
  const ProUpgradeSection = useMemo(() => {
    if (settings.isPro) {
      return (
        <View style={styles.section}>
          <View style={styles.proHeader}>
            <Text style={styles.proTitle}>✨ MiddleCrop Pro</Text>
            <Text style={styles.proSubtitle}>You have unlimited access!</Text>
            <Text style={styles.proFeatures}>
              Unlimited crops • All formats • Quality controls • Unlimited history
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.section}>
        <View style={styles.upgradeContainer}>
          <Text style={styles.upgradeTitle}>Upgrade to MiddleCrop Pro</Text>
          <Text style={styles.upgradeSubtitle}>Unlock unlimited crops and all features</Text>

          <View style={styles.featureList}>
            <Text style={styles.featureItem}>✓ Unlimited daily crops</Text>
            <Text style={styles.featureItem}>✓ All export formats (PNG, WEBP, JPEG)</Text>
            <Text style={styles.featureItem}>✓ Quality adjustment controls</Text>
            <Text style={styles.featureItem}>✓ Unlimited crop history</Text>
            <Text style={styles.featureItem}>✓ Priority support</Text>
          </View>

          <GradientButton
            title={`Upgrade for ${productInfo.price}`}
            onPress={handleUpgradeToPro}
            disabled={purchaseStatus.isLoading}
            isLoading={purchaseStatus.isLoading}
            gradientBackgroundProps={{
              colors: [colors.primary, colors.secondary],
              start: { x: 0, y: 0 },
              end: { x: 1, y: 1 },
            }}
            style={styles.upgradeButton}
          />

          <Button
            title="Restore Purchases"
            onPress={handleRestorePurchases}
            disabled={purchaseStatus.isRestoring}
            style={styles.restoreButton}
          />
        </View>
      </View>
    );
  }, [
    settings.isPro,
    productInfo.price,
    purchaseStatus,
    handleUpgradeToPro,
    handleRestorePurchases,
  ]);

  // Export settings section
  const ExportSettingsSection = useMemo(
    () => (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Export Settings</Text>

        {/* Format Selection */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Export Format</Text>
          <View style={styles.formatButtons}>
            {availableFormats.map(format => (
              <TouchableOpacity
                key={format}
                style={[
                  styles.formatButton,
                  exportConfig.format === format && styles.activeFormatButton,
                ]}
                onPress={() => handleFormatChange(format)}>
                <Text
                  style={[
                    styles.formatButtonText,
                    exportConfig.format === format && styles.activeFormatButtonText,
                  ]}>
                  {format.toUpperCase()}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {!settings.isPro && (
            <Text style={styles.settingNote}>Pro required for PNG and WEBP formats</Text>
          )}
        </View>

        {/* Quality Settings */}
        {canAdjustQuality && (
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Quality: {settings.quality}%</Text>
            <View style={styles.qualityButtons}>
              {Object.entries(QUALITY_PRESETS).map(([label, value]) => (
                <TouchableOpacity
                  key={label}
                  style={[
                    styles.qualityButton,
                    settings.quality === value && styles.activeQualityButton,
                  ]}
                  onPress={() => handleQualityChange(value)}>
                  <Text
                    style={[
                      styles.qualityButtonText,
                      settings.quality === value && styles.activeQualityButtonText,
                    ]}>
                    {label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </View>
    ),
    [
      availableFormats,
      canAdjustQuality,
      exportConfig.format,
      settings.quality,
      settings.isPro,
      handleFormatChange,
      handleQualityChange,
    ],
  );

  // App settings section
  const AppSettingsSection = useMemo(
    () => (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Settings</Text>

        {/* Theme Selection */}
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Theme</Text>
          <View style={styles.themeButtons}>
            {(['system', 'light', 'dark'] as const).map(theme => (
              <TouchableOpacity
                key={theme}
                style={[styles.themeButton, settings.theme === theme && styles.activeThemeButton]}
                onPress={() => handleThemeChange(theme)}>
                <Text
                  style={[
                    styles.themeButtonText,
                    settings.theme === theme && styles.activeThemeButtonText,
                  ]}>
                  {theme.charAt(0).toUpperCase() + theme.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Haptic Feedback Toggle */}
        <View style={styles.settingItem}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Haptic Feedback</Text>
              <Text style={styles.settingDescription}>Vibration feedback for interactions</Text>
            </View>
            <Switch
              value={settings.hapticFeedback}
              onValueChange={value => {
                dispatch(setHapticFeedback(value));
              }}
              trackColor={{ false: colors.disabled, true: colors.primary }}
              thumbColor={settings.hapticFeedback ? colors.background : colors.textSecondary}
            />
          </View>
        </View>

        {/* Auto Save Toggle */}
        <View style={styles.settingItem}>
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Auto Save to Gallery</Text>
              <Text style={styles.settingDescription}>
                Automatically save crops to photo library
              </Text>
            </View>
            <Switch
              value={settings.autoSaveToGallery}
              onValueChange={value => {
                dispatch(setAutoSaveToGallery(value));
              }}
              trackColor={{ false: colors.disabled, true: colors.primary }}
              thumbColor={settings.autoSaveToGallery ? colors.background : colors.textSecondary}
            />
          </View>
        </View>
      </View>
    ),
    [
      settings.theme,
      settings.hapticFeedback,
      settings.autoSaveToGallery,
      dispatch,
      handleThemeChange,
    ],
  );

  // App info section
  const AppInfoSection = useMemo(
    () => (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>About</Text>

        <View style={styles.infoGrid}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>{APP_CONFIG.version}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Organization</Text>
            <Text style={styles.infoValue}>{APP_CONFIG.organization}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>User Tier</Text>
            <Text style={[styles.infoValue, userTier.isPro && styles.proText]}>
              {userTier.tier.toUpperCase()}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Bundle ID</Text>
            <Text style={styles.infoValue}>{APP_CONFIG.bundleId}</Text>
          </View>
        </View>

        <View style={styles.linkButtons}>
          <Button
            title="Contact Support"
            onPress={handleContactSupport}
            style={styles.supportButton}
          />

          <View style={styles.legalLinks}>
            <TouchableOpacity onPress={handlePrivacyPolicy} style={styles.legalButton}>
              <Text style={styles.legalButtonText}>Privacy Policy</Text>
            </TouchableOpacity>
            <Text style={styles.legalSeparator}>•</Text>
            <TouchableOpacity onPress={handleTerms} style={styles.legalButton}>
              <Text style={styles.legalButtonText}>Terms of Service</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    ),
    [userTier, handleContactSupport, handlePrivacyPolicy, handleTerms],
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
        <Text style={styles.subtitle}>Customize your MiddleCrop experience</Text>
      </View>

      {ProUpgradeSection}
      {ExportSettingsSection}
      {AppSettingsSection}
      {AppInfoSection}

      <View style={styles.footer}>
        <Text style={styles.footerText}>Made with ❤️ by {APP_CONFIG.organization}</Text>
        <Text style={styles.footerText}>
          Version {APP_CONFIG.version} • Build {APP_CONFIG.buildNumber}
        </Text>
        <Text style={styles.footerSubtext}>
          MiddleCrop helps you remove unwanted sections from images while seamlessly stitching the
          remaining parts together.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.surface,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.text,
    fontFamily: fonts.bold,
  },
  subtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 4,
    fontFamily: fonts.regular,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
    fontFamily: fonts.semibold,
  },
  settingItem: {
    marginBottom: 20,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
    fontFamily: fonts.medium,
  },
  settingDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  settingNote: {
    fontSize: 12,
    color: colors.warning,
    marginTop: 8,
    fontStyle: 'italic',
    fontFamily: fonts.regular,
  },
  formatButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  formatButton: {
    flex: 1,
    backgroundColor: colors.background,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.border,
    alignItems: 'center',
  },
  activeFormatButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  formatButtonText: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
  activeFormatButtonText: {
    color: 'white',
  },
  qualityButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  qualityButton: {
    backgroundColor: colors.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors.border,
  },
  activeQualityButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  qualityButtonText: {
    fontSize: 12,
    color: colors.text,
    fontWeight: '500',
    fontFamily: fonts.medium,
  },
  activeQualityButtonText: {
    color: 'white',
  },
  themeButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  themeButton: {
    flex: 1,
    backgroundColor: colors.background,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.border,
    alignItems: 'center',
  },
  activeThemeButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  themeButtonText: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '500',
    fontFamily: fonts.medium,
  },
  activeThemeButtonText: {
    color: 'white',
  },
  upgradeContainer: {
    alignItems: 'center',
    backgroundColor: colors.secondary + '20',
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.secondary + '40',
  },
  upgradeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
    fontFamily: fonts.bold,
  },
  upgradeSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  featureList: {
    alignSelf: 'stretch',
    marginBottom: 24,
  },
  featureItem: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 8,
    paddingLeft: 8,
    fontFamily: fonts.regular,
  },
  upgradeButton: {
    width: '100%',
    marginBottom: 16,
    borderRadius: 12,
    paddingVertical: 16,
  },
  restoreButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  restoreButtonText: {
    color: colors.primary,
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
  proHeader: {
    alignItems: 'center',
    backgroundColor: colors.accent + '20',
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: colors.accent + '40',
  },
  proTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.accent,
    marginBottom: 4,
    fontFamily: fonts.bold,
  },
  proSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
    fontFamily: fonts.regular,
  },
  proFeatures: {
    fontSize: 12,
    color: colors.accent,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  infoItem: {
    flex: 1,
    minWidth: 140,
    backgroundColor: colors.surface,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
    fontFamily: fonts.regular,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    fontFamily: fonts.semibold,
  },
  proText: {
    color: colors.accent,
  },
  linkButtons: {
    gap: 16,
  },
  supportButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
  },
  supportButtonText: {
    color: 'white',
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
  legalLinks: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  legalButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  legalButtonText: {
    fontSize: 14,
    color: colors.primary,
    textDecorationLine: 'underline',
    fontFamily: fonts.regular,
  },
  legalSeparator: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  footer: {
    padding: 24,
    alignItems: 'center',
    backgroundColor: colors.surface,
  },
  footerText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 4,
    fontFamily: fonts.regular,
  },
  footerSubtext: {
    fontSize: 11,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
    fontFamily: fonts.regular,
  },
});
