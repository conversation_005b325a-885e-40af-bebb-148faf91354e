import React, { useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Alert, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';

import Button from '../../components/Button';
import GradientButton from '../../components/GradientButton';
import CropPreview from '../../components/CropPreview';
import { useAppSelector, useAppDispatch } from '../../utils/store';
import { HybridCropService } from '../../modules/crop/crop.service';
import {
  setSelectedImage,
  setCropRegion,
  setCropMode,
  setProcessing,
  setProcessingProgress,
  addCropToHistory,
  setError,
  selectCanCrop,
  selectRemainingCrops,
} from '../../modules/crop/crop.slice';
import { CropMode, ExportFormat, ErrorType } from '../../modules/crop/crop.typeDefs';
import { colors, fonts } from '../../theme';

export default function Home() {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  // Redux state
  const selectedImage = useAppSelector(state => state.crop.selectedImage);
  const cropRegion = useAppSelector(state => state.crop.cropRegion);
  const cropMode = useAppSelector(state => state.crop.cropMode);
  const isProcessing = useAppSelector(state => state.crop.isProcessing);
  const processingProgress = useAppSelector(state => state.crop.processingProgress);
  const error = useAppSelector(state => state.crop.error);
  const canCrop = useAppSelector(selectCanCrop);
  const remainingCrops = useAppSelector(selectRemainingCrops);

  const isPro = useAppSelector(state => state.settings.isPro);
  const exportFormat = useAppSelector(state => state.settings.exportFormat);
  const quality = useAppSelector(state => state.settings.quality);
  const hapticFeedback = useAppSelector(state => state.settings.hapticFeedback);

  // Image selection handler
  const handlePickImage = useCallback(async () => {
    try {
      if (hapticFeedback) {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

      const pickedImage = await HybridCropService.pickImage();
      if (pickedImage) {
        dispatch(setSelectedImage(pickedImage));
        dispatch(setCropRegion(null));
      }
    } catch (error) {
      console.error('Failed to pick image:', error);
      dispatch(
        setError({
          type: ErrorType.IMAGE_LOAD_FAILED,
          message: error instanceof Error ? error.message : 'Failed to pick image',
          timestamp: Date.now(),
          retryable: true,
        }),
      );
    }
  }, [dispatch, hapticFeedback]);

  // Crop mode change handler
  const handleCropModeChange = useCallback(
    (newMode: CropMode) => {
      if (hapticFeedback) {
        Haptics.selectionAsync();
      }
      dispatch(setCropMode(newMode));
    },
    [dispatch, hapticFeedback],
  );

  // Crop region change handler
  const handleCropRegionChange = useCallback(
    (region: any) => {
      dispatch(setCropRegion(region));
    },
    [dispatch],
  );

  // Main crop processing handler
  const handleCropImage = useCallback(async () => {
    if (!selectedImage || !cropRegion) {
      Alert.alert('Error', 'Please select an image and crop region first');
      return;
    }

    if (!canCrop) {
      if (!isPro && remainingCrops === 0) {
        Alert.alert(
          'Daily Limit Reached',
          "You've reached your daily crop limit. Upgrade to Pro for unlimited crops!",
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Upgrade to Pro', onPress: () => navigation.navigate('ProUpgrade' as never) },
          ],
        );
        return;
      }
      Alert.alert('Error', 'Cannot crop image at this time');
      return;
    }

    try {
      dispatch(setProcessing(true));
      dispatch(setProcessingProgress(0));

      if (hapticFeedback) {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
      }

      // Load Skia image for processing
      dispatch(setProcessingProgress(20));
      const skiaImage = await HybridCropService.loadSkiaImage(selectedImage.uri);

      // Perform middle crop with progress updates
      dispatch(setProcessingProgress(40));
      const croppedImage = await HybridCropService.performMiddleCrop(
        skiaImage,
        selectedImage.dimensions,
        cropRegion,
        progress => dispatch(setProcessingProgress(40 + progress * 0.4)),
      );

      // Save processed image
      dispatch(setProcessingProgress(85));
      const savedUri = await HybridCropService.saveProcessedImage(
        croppedImage,
        exportFormat,
        quality,
      );

      // Add to history
      const processingEndTime = Date.now();
      const historyEntry = {
        id: `crop_${processingEndTime}`,
        originalImageUri: selectedImage.uri,
        resultImageUri: savedUri,
        timestamp: processingEndTime,
        cropRegion,
        format: exportFormat,
        quality,
        originalDimensions: selectedImage.dimensions,
        resultDimensions: HybridCropService.calculateResultDimensions(
          selectedImage.dimensions,
          cropRegion,
        ),
        processingTimeMs: 1000, // Approximate processing time
      };

      dispatch(addCropToHistory(historyEntry));
      dispatch(setProcessingProgress(100));

      // Success feedback
      if (hapticFeedback) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      Alert.alert('Success!', 'Image cropped and saved successfully', [
        { text: 'View History', onPress: () => navigation.navigate('History' as never) },
        { text: 'Crop Another', onPress: handlePickImage },
        { text: 'OK', style: 'default' },
      ]);
    } catch (error) {
      console.error('Crop processing failed:', error);
      dispatch(
        setError({
          type: ErrorType.PROCESSING_FAILED,
          message: error instanceof Error ? error.message : 'Failed to process image',
          timestamp: Date.now(),
          retryable: true,
        }),
      );

      if (hapticFeedback) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    } finally {
      dispatch(setProcessing(false));
    }
  }, [
    selectedImage,
    cropRegion,
    canCrop,
    isPro,
    remainingCrops,
    exportFormat,
    quality,
    hapticFeedback,
    dispatch,
    navigation,
    handlePickImage,
  ]);

  // Crop mode selector
  const CropModeSelector = useMemo(() => {
    const modes = [
      {
        mode: CropMode.HORIZONTAL_STRIP,
        title: 'Horizontal',
        icon: '—',
        description: 'Remove horizontal strip',
      },
      {
        mode: CropMode.VERTICAL_STRIP,
        title: 'Vertical',
        icon: '|',
        description: 'Remove vertical strip',
      },
      {
        mode: CropMode.RECTANGULAR,
        title: 'Rectangle',
        icon: '▢',
        description: 'Remove any rectangle',
      },
    ];

    return (
      <View style={styles.modeSelector}>
        <Text style={styles.modeSelectorTitle}>Crop Mode</Text>
        <View style={styles.modeButtons}>
          {modes.map(({ mode, title, icon, description }) => (
            <TouchableOpacity
              key={mode}
              style={[styles.modeButton, cropMode === mode && styles.activeModeButton]}
              onPress={() => handleCropModeChange(mode)}
              activeOpacity={0.7}>
              <Text
                style={[styles.modeButtonIcon, cropMode === mode && styles.activeModeButtonIcon]}>
                {icon}
              </Text>
              <Text
                style={[styles.modeButtonText, cropMode === mode && styles.activeModeButtonText]}>
                {title}
              </Text>
              <Text
                style={[
                  styles.modeButtonDescription,
                  cropMode === mode && styles.activeModeButtonDescription,
                ]}>
                {description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  }, [cropMode, handleCropModeChange]);

  // Status indicator
  const StatusIndicator = useMemo(() => {
    if (isProcessing) {
      return (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>Processing... {Math.round(processingProgress)}%</Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${processingProgress}%` }]} />
          </View>
        </View>
      );
    }

    if (!isPro && remainingCrops !== Infinity) {
      return (
        <View style={[styles.statusContainer, styles.warningStatus]}>
          <Text style={styles.warningText}>Free: {remainingCrops} crops remaining today</Text>
          {remainingCrops === 0 && (
            <TouchableOpacity
              style={styles.upgradeLink}
              onPress={() => navigation.navigate('ProUpgrade' as never)}>
              <Text style={styles.upgradeLinkText}>Upgrade to Pro</Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }

    if (isPro) {
      return (
        <View style={[styles.statusContainer, styles.proStatus]}>
          <Text style={styles.proText}>✨ MiddleCrop Pro - Unlimited</Text>
        </View>
      );
    }

    return null;
  }, [isProcessing, processingProgress, isPro, remainingCrops, navigation]);

  // Main content area
  const MainContent = useMemo(() => {
    if (!selectedImage) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateIcon}>🎯</Text>
          <Text style={styles.emptyStateTitle}>Welcome to MiddleCrop</Text>
          <Text style={styles.emptyStateSubtitle}>
            Select an image to start removing unwanted sections and seamlessly stitch the remaining
            parts together
          </Text>
          <View style={styles.featureList}>
            <Text style={styles.featureItem}>📸 Select from gallery</Text>
            <Text style={styles.featureItem}>✂️ Choose what to remove</Text>
            <Text style={styles.featureItem}>🔗 Auto-stitch remaining parts</Text>
            <Text style={styles.featureItem}>💾 Export in multiple formats</Text>
          </View>
        </View>
      );
    }

    return (
      <>
        <View style={styles.previewContainer}>
          <CropPreview
            image={selectedImage}
            cropMode={cropMode}
            onCropRegionChange={handleCropRegionChange}
            onError={error => {
              console.error('CropPreview error:', error);
              dispatch(
                setError({
                  type: ErrorType.PROCESSING_FAILED,
                  message: error.message,
                  timestamp: Date.now(),
                  retryable: true,
                }),
              );
            }}
          />
        </View>
        {CropModeSelector}
      </>
    );
  }, [selectedImage, cropMode, handleCropRegionChange, dispatch, CropModeSelector]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>MiddleCrop</Text>
        <Text style={styles.subtitle}>Remove the middle, keep the rest</Text>
        {StatusIndicator}
      </View>

      <View style={styles.content}>{MainContent}</View>

      <View style={styles.actions}>
        {selectedImage && cropRegion ? (
          <GradientButton
            title={
              isProcessing ? `Processing ${Math.round(processingProgress)}%...` : 'Crop & Save'
            }
            onPress={handleCropImage}
            disabled={!canCrop || isProcessing}
            style={[styles.primaryAction, (!canCrop || isProcessing) && styles.disabledAction]}
            gradientBackgroundProps={{
              colors: [colors.primary, colors.secondary],
              start: { x: 0, y: 0 },
              end: { x: 1, y: 0 },
            }}
          />
        ) : null}

        <Button
          title={selectedImage ? 'Pick Different Image' : 'Pick Image to Start'}
          onPress={handlePickImage}
          disabled={isProcessing}
          style={styles.secondaryAction}
        />

        {!isPro && (
          <TouchableOpacity
            style={styles.proButton}
            onPress={() => navigation.navigate('ProUpgrade' as never)}>
            <Text style={styles.proButtonText}>🚀 Upgrade to Pro - Unlimited Crops</Text>
          </TouchableOpacity>
        )}
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>⚠️ {error}</Text>
          <TouchableOpacity style={styles.errorButton} onPress={() => dispatch(setError(null))}>
            <Text style={styles.errorButtonText}>Dismiss</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.surface,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.text,
    fontFamily: fonts.bold,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
    fontFamily: fonts.regular,
  },
  content: {
    flex: 1,
  },
  previewContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#000',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  modeSelector: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  modeSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
    textAlign: 'center',
    fontFamily: fonts.semibold,
  },
  modeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 8,
  },
  modeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    backgroundColor: colors.background,
    borderWidth: 2,
    borderColor: colors.border,
  },
  activeModeButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  modeButtonIcon: {
    fontSize: 20,
    color: colors.text,
    marginBottom: 4,
  },
  activeModeButtonIcon: {
    color: 'white',
  },
  modeButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
    fontFamily: fonts.semibold,
  },
  activeModeButtonText: {
    color: 'white',
  },
  modeButtonDescription: {
    fontSize: 10,
    color: colors.textSecondary,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  activeModeButtonDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: fonts.semibold,
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
    fontFamily: fonts.regular,
  },
  featureList: {
    alignSelf: 'stretch',
    paddingHorizontal: 20,
  },
  featureItem: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: fonts.regular,
  },
  actions: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: 12,
  },
  primaryAction: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
  },
  disabledAction: {
    backgroundColor: colors.disabled,
    opacity: 0.6,
  },
  secondaryAction: {
    backgroundColor: colors.background,
    borderWidth: 2,
    borderColor: colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
  },
  proButton: {
    backgroundColor: colors.secondary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  proButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    fontFamily: fonts.semibold,
  },
  statusContainer: {
    marginTop: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  warningStatus: {
    backgroundColor: colors.warning + '20',
    borderWidth: 1,
    borderColor: colors.warning,
  },
  proStatus: {
    backgroundColor: colors.secondary + '20',
    borderWidth: 1,
    borderColor: colors.secondary,
  },
  statusText: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: fonts.regular,
  },
  warningText: {
    fontSize: 14,
    color: colors.warning,
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
  proText: {
    fontSize: 14,
    color: colors.secondary,
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
  upgradeLink: {
    marginTop: 4,
  },
  upgradeLinkText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    textDecorationLine: 'underline',
    fontFamily: fonts.semibold,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: colors.border,
    borderRadius: 2,
    marginTop: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  errorContainer: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: colors.error,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  errorText: {
    flex: 1,
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 12,
    fontFamily: fonts.regular,
  },
  errorButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  errorButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    fontFamily: fonts.semibold,
  },
});
