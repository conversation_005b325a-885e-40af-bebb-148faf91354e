import React, { useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';

import Button from '../../components/Button';
import { useAppSelector, useAppDispatch } from '../../utils/store';
import { HybridCropService } from '../../modules/crop/crop.service';
import {
  removeCropFromHistory,
  clearCropHistory,
  selectHistoryStats,
  selectRecentHistory,
} from '../../modules/crop/crop.slice';
import { CropHistoryEntry, CropMode } from '../../modules/crop/crop.typeDefs';
import { colors, fonts } from '../../theme';

export default function Details() {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  // Redux state
  const cropHistory = useAppSelector(state => state.crop.cropHistory);
  const historyStats = useAppSelector(selectHistoryStats);
  const recentHistory = useAppSelector(state => selectRecentHistory(state, 3));
  const isPro = useAppSelector(state => state.settings.isPro);
  const hapticFeedback = useAppSelector(state => state.settings.hapticFeedback);

  // Share crop result
  const handleShareCrop = useCallback(
    async (entry: CropHistoryEntry) => {
      try {
        if (hapticFeedback) {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }

        await HybridCropService.shareImage(entry.resultImageUri);
      } catch (error) {
        console.error('Failed to share image:', error);
        Alert.alert('Error', 'Failed to share image');
      }
    },
    [hapticFeedback],
  );

  // Delete crop from history
  const handleDeleteCrop = useCallback(
    async (entry: CropHistoryEntry) => {
      if (hapticFeedback) {
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      Alert.alert('Delete Crop', 'Are you sure you want to delete this crop from history?', [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            dispatch(removeCropFromHistory(entry.id));

            // Clean up file
            try {
              await HybridCropService.deleteImage(entry.resultImageUri);
            } catch (error) {
              console.warn('Failed to delete image file:', error);
            }
          },
        },
      ]);
    },
    [dispatch, hapticFeedback],
  );

  // Clear all history
  const handleClearHistory = useCallback(() => {
    if (cropHistory.length === 0) return;

    Alert.alert(
      'Clear History',
      `Delete all ${cropHistory.length} crop${cropHistory.length === 1 ? '' : 's'} from history?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            dispatch(clearCropHistory());

            // Clean up files
            for (const entry of cropHistory) {
              try {
                await HybridCropService.deleteImage(entry.resultImageUri);
              } catch (error) {
                console.warn('Failed to delete image file:', error);
              }
            }

            if (hapticFeedback) {
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            }
          },
        },
      ],
    );
  }, [cropHistory, dispatch, hapticFeedback]);

  // Format crop mode for display
  const formatCropMode = useCallback((mode: CropMode) => {
    switch (mode) {
      case CropMode.HORIZONTAL_STRIP:
        return '— Horizontal';
      case CropMode.VERTICAL_STRIP:
        return '| Vertical';
      case CropMode.RECTANGULAR:
        return '⬜ Rectangle';
      default:
        return mode;
    }
  }, []);

  // Render history item
  const renderHistoryItem = useCallback(
    ({ item }: { item: CropHistoryEntry }) => (
      <TouchableOpacity style={styles.historyItem}>
        <View style={styles.historyItemContent}>
          <Image
            source={{ uri: item.resultImageUri }}
            style={styles.thumbnail}
            resizeMode="cover"
          />

          <View style={styles.historyItemInfo}>
            <Text style={styles.historyItemTitle}>{formatCropMode(item.cropRegion.cropMode)}</Text>
            <Text style={styles.historyItemSubtitle}>
              {item.resultDimensions.width}×{item.resultDimensions.height} •{' '}
              {item.format.toUpperCase()}
            </Text>
            <Text style={styles.historyItemTime}>
              {new Date(item.timestamp).toLocaleDateString()} at{' '}
              {new Date(item.timestamp).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Text>
          </View>

          <View style={styles.historyItemActions}>
            <TouchableOpacity style={styles.actionButton} onPress={() => handleShareCrop(item)}>
              <Text style={styles.actionButtonText}>📤</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={() => handleDeleteCrop(item)}>
              <Text style={styles.actionButtonText}>🗑️</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    ),
    [formatCropMode, handleShareCrop, handleDeleteCrop],
  );

  // History stats component
  const HistoryStats = useMemo(() => {
    if (cropHistory.length === 0) return null;

    return (
      <View style={styles.statsContainer}>
        <Text style={styles.statsTitle}>Statistics</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{historyStats.totalCrops}</Text>
            <Text style={styles.statLabel}>Total Crops</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {historyStats.averageProcessingTime
                ? `${Math.round(historyStats.averageProcessingTime)}ms`
                : 'N/A'}
            </Text>
            <Text style={styles.statLabel}>Avg Processing</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {Object.keys(historyStats.modeDistribution).length}
            </Text>
            <Text style={styles.statLabel}>Modes Used</Text>
          </View>
        </View>
      </View>
    );
  }, [cropHistory.length, historyStats]);

  // Empty state
  const EmptyState = useMemo(
    () => (
      <View style={styles.emptyState}>
        <Text style={styles.emptyStateIcon}>📱</Text>
        <Text style={styles.emptyStateTitle}>No Crops Yet</Text>
        <Text style={styles.emptyStateSubtitle}>
          Start cropping images and they'll appear in your history
        </Text>
        <Button
          title="Start Cropping"
          onPress={() => navigation.navigate('Home' as never)}
          style={styles.emptyStateButton}
        />
      </View>
    ),
    [navigation],
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Crop History</Text>
        {cropHistory.length > 0 && (
          <TouchableOpacity onPress={handleClearHistory}>
            <Text style={styles.clearButton}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>

      {cropHistory.length === 0 ? (
        EmptyState
      ) : (
        <>
          {HistoryStats}

          <FlatList
            data={cropHistory}
            renderItem={renderHistoryItem}
            keyExtractor={item => item.id}
            style={styles.list}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        </>
      )}

      {!isPro && cropHistory.length >= 10 && (
        <View style={styles.upgradePrompt}>
          <Text style={styles.upgradeText}>History limited to 10 items on free plan</Text>
          <Button
            title="Upgrade to Pro"
            onPress={() => navigation.navigate('ProUpgrade' as never)}
            style={styles.upgradeButton}
          />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
    fontFamily: fonts.bold,
  },
  clearButton: {
    fontSize: 16,
    color: colors.error,
    fontWeight: '600',
  },
  statsContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 12,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingHorizontal: 16,
  },
  historyItem: {
    marginBottom: 12,
    backgroundColor: colors.surface,
    borderRadius: 12,
    overflow: 'hidden',
  },
  historyItemContent: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: colors.lightGray,
  },
  historyItemInfo: {
    flex: 1,
    marginLeft: 12,
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  historyItemSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  historyItemTime: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  historyItemActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: colors.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyStateButton: {
    paddingHorizontal: 32,
  },
  upgradePrompt: {
    margin: 16,
    padding: 16,
    backgroundColor: colors.warning + '20',
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  upgradeText: {
    flex: 1,
    fontSize: 14,
    color: colors.text,
    marginRight: 12,
  },
  upgradeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
