import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { GestureHandlerRootView, GestureDetector, Gesture } from 'react-native-gesture-handler';
import {
  Canvas,
  Image as SkiaImage,
  Rect,
  Skia,
  useComputedValue,
  useImage,
} from '@shopify/react-native-skia';
import Animated, { useSharedValue, runOnJS } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { useAppSelector } from '../../utils/store';
import {
  MiddleCropImage,
  CropRegion,
  CropMode,
  PROCESSING_LIMITS,
} from '../../modules/crop/crop.typeDefs';

interface CropPreviewProps {
  image: MiddleCropImage;
  cropMode: CropMode;
  onCropRegionChange: (region: CropRegion | null) => void;
  onError?: (error: Error) => void;
}

const HANDLE_SIZE = 24;
const OVERLAY_COLOR = 'rgba(0, 0, 0, 0.6)';
const BORDER_COLOR = '#007AFF';

export const CropPreview: React.FC<CropPreviewProps> = ({
  image,
  cropMode,
  onCropRegionChange,
  onError,
}) => {
  const hapticFeedback = useAppSelector(state => state.settings.hapticFeedback);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  // Crop bounds in image coordinates
  const cropLeft = useSharedValue(0);
  const cropTop = useSharedValue(0);
  const cropRight = useSharedValue(0);
  const cropBottom = useSharedValue(0);

  const skiaImage = useImage(image.uri);

  // Calculate image display dimensions and position
  const imageLayout = useComputedValue(() => {
    if (!skiaImage || containerSize.width === 0 || containerSize.height === 0) {
      return { x: 0, y: 0, width: 0, height: 0, scale: 1 };
    }

    const imageAspectRatio = image.dimensions.width / image.dimensions.height;
    const containerAspectRatio = containerSize.width / containerSize.height;

    let displayWidth: number;
    let displayHeight: number;
    let x: number;
    let y: number;

    if (imageAspectRatio > containerAspectRatio) {
      displayWidth = containerSize.width;
      displayHeight = displayWidth / imageAspectRatio;
      x = 0;
      y = (containerSize.height - displayHeight) / 2;
    } else {
      displayHeight = containerSize.height;
      displayWidth = displayHeight * imageAspectRatio;
      x = (containerSize.width - displayWidth) / 2;
      y = 0;
    }

    const scale = displayWidth / image.dimensions.width;
    return { x, y, width: displayWidth, height: displayHeight, scale };
  }, [skiaImage, image.dimensions, containerSize]);

  // Initialize crop region based on mode
  const initializeCropRegion = useCallback(() => {
    const layout = imageLayout.current;
    if (layout.width === 0 || layout.height === 0) return;

    const { width: imgWidth, height: imgHeight } = image.dimensions;

    switch (cropMode) {
      case CropMode.HORIZONTAL_STRIP:
        cropLeft.value = 0;
        cropRight.value = imgWidth;
        cropTop.value = imgHeight * 0.35;
        cropBottom.value = imgHeight * 0.65;
        break;
      case CropMode.VERTICAL_STRIP:
        cropLeft.value = imgWidth * 0.35;
        cropRight.value = imgWidth * 0.65;
        cropTop.value = 0;
        cropBottom.value = imgHeight;
        break;
      case CropMode.RECTANGULAR:
        cropLeft.value = imgWidth * 0.25;
        cropRight.value = imgWidth * 0.75;
        cropTop.value = imgHeight * 0.25;
        cropBottom.value = imgHeight * 0.75;
        break;
    }

    updateCropRegion();
  }, [cropMode, image.dimensions, imageLayout]);

  // Update crop region and notify parent
  const updateCropRegion = useCallback(() => {
    const region: CropRegion = {
      x: cropLeft.value,
      y: cropTop.value,
      width: cropRight.value - cropLeft.value,
      height: cropBottom.value - cropTop.value,
      cropMode,
    };

    onCropRegionChange(region);
  }, [cropMode, onCropRegionChange, cropLeft, cropTop, cropRight, cropBottom]);

  // Initialize crop region when image or mode changes
  useEffect(() => {
    if (skiaImage) {
      initializeCropRegion();
    }
  }, [skiaImage, initializeCropRegion]);

  // Create gesture for handle dragging
  const createHandleGesture = useCallback(
    (
      handleType:
        | 'topLeft'
        | 'topRight'
        | 'bottomLeft'
        | 'bottomRight'
        | 'top'
        | 'bottom'
        | 'left'
        | 'right',
    ) => {
      return Gesture.Pan()
        .onStart(() => {
          if (hapticFeedback) {
            runOnJS(Haptics.selectionAsync)();
          }
        })
        .onUpdate(event => {
          const layout = imageLayout.current;
          if (layout.scale === 0) return;

          const deltaX = event.translationX / layout.scale;
          const deltaY = event.translationY / layout.scale;

          const { width: imgWidth, height: imgHeight } = image.dimensions;
          const minSize = PROCESSING_LIMITS.minCropSize;

          switch (handleType) {
            case 'topLeft':
              if (cropMode === CropMode.RECTANGULAR) {
                cropLeft.value = Math.max(
                  0,
                  Math.min(cropLeft.value + deltaX, cropRight.value - minSize),
                );
                cropTop.value = Math.max(
                  0,
                  Math.min(cropTop.value + deltaY, cropBottom.value - minSize),
                );
              }
              break;
            case 'topRight':
              if (cropMode === CropMode.RECTANGULAR) {
                cropRight.value = Math.min(
                  imgWidth,
                  Math.max(cropRight.value + deltaX, cropLeft.value + minSize),
                );
                cropTop.value = Math.max(
                  0,
                  Math.min(cropTop.value + deltaY, cropBottom.value - minSize),
                );
              }
              break;
            case 'bottomLeft':
              if (cropMode === CropMode.RECTANGULAR) {
                cropLeft.value = Math.max(
                  0,
                  Math.min(cropLeft.value + deltaX, cropRight.value - minSize),
                );
                cropBottom.value = Math.min(
                  imgHeight,
                  Math.max(cropBottom.value + deltaY, cropTop.value + minSize),
                );
              }
              break;
            case 'bottomRight':
              if (cropMode === CropMode.RECTANGULAR) {
                cropRight.value = Math.min(
                  imgWidth,
                  Math.max(cropRight.value + deltaX, cropLeft.value + minSize),
                );
                cropBottom.value = Math.min(
                  imgHeight,
                  Math.max(cropBottom.value + deltaY, cropTop.value + minSize),
                );
              }
              break;
            case 'top':
              if (cropMode === CropMode.HORIZONTAL_STRIP || cropMode === CropMode.RECTANGULAR) {
                cropTop.value = Math.max(
                  0,
                  Math.min(cropTop.value + deltaY, cropBottom.value - minSize),
                );
              }
              break;
            case 'bottom':
              if (cropMode === CropMode.HORIZONTAL_STRIP || cropMode === CropMode.RECTANGULAR) {
                cropBottom.value = Math.min(
                  imgHeight,
                  Math.max(cropBottom.value + deltaY, cropTop.value + minSize),
                );
              }
              break;
            case 'left':
              if (cropMode === CropMode.VERTICAL_STRIP || cropMode === CropMode.RECTANGULAR) {
                cropLeft.value = Math.max(
                  0,
                  Math.min(cropLeft.value + deltaX, cropRight.value - minSize),
                );
              }
              break;
            case 'right':
              if (cropMode === CropMode.VERTICAL_STRIP || cropMode === CropMode.RECTANGULAR) {
                cropRight.value = Math.min(
                  imgWidth,
                  Math.max(cropRight.value + deltaX, cropLeft.value + minSize),
                );
              }
              break;
          }
        })
        .onEnd(() => {
          if (hapticFeedback) {
            runOnJS(Haptics.impactAsync)(Haptics.ImpactFeedbackStyle.Light);
          }
          runOnJS(updateCropRegion)();
        });
    },
    [
      cropMode,
      image.dimensions,
      imageLayout,
      hapticFeedback,
      updateCropRegion,
      cropLeft,
      cropTop,
      cropRight,
      cropBottom,
    ],
  );

  // Render crop handles based on mode
  const renderHandles = useMemo(() => {
    const layout = imageLayout.current;
    if (layout.width === 0) return null;

    const handles: React.ReactNode[] = [];

    const getScreenCoords = (imageX: number, imageY: number) => ({
      x: layout.x + imageX * layout.scale,
      y: layout.y + imageY * layout.scale,
    });

    if (cropMode === CropMode.HORIZONTAL_STRIP) {
      // Top and bottom handles
      const topCoords = getScreenCoords(0, cropTop.value);
      const bottomCoords = getScreenCoords(0, cropBottom.value);

      handles.push(
        <GestureDetector key="top" gesture={createHandleGesture('top')}>
          <Animated.View
            style={[
              styles.horizontalHandle,
              {
                position: 'absolute',
                left: layout.x,
                top: topCoords.y - HANDLE_SIZE / 2,
                width: layout.width,
                height: HANDLE_SIZE,
              },
            ]}
          />
        </GestureDetector>,
        <GestureDetector key="bottom" gesture={createHandleGesture('bottom')}>
          <Animated.View
            style={[
              styles.horizontalHandle,
              {
                position: 'absolute',
                left: layout.x,
                top: bottomCoords.y - HANDLE_SIZE / 2,
                width: layout.width,
                height: HANDLE_SIZE,
              },
            ]}
          />
        </GestureDetector>,
      );
    } else if (cropMode === CropMode.VERTICAL_STRIP) {
      // Left and right handles
      const leftCoords = getScreenCoords(cropLeft.value, 0);
      const rightCoords = getScreenCoords(cropRight.value, 0);

      handles.push(
        <GestureDetector key="left" gesture={createHandleGesture('left')}>
          <Animated.View
            style={[
              styles.verticalHandle,
              {
                position: 'absolute',
                left: leftCoords.x - HANDLE_SIZE / 2,
                top: layout.y,
                width: HANDLE_SIZE,
                height: layout.height,
              },
            ]}
          />
        </GestureDetector>,
        <GestureDetector key="right" gesture={createHandleGesture('right')}>
          <Animated.View
            style={[
              styles.verticalHandle,
              {
                position: 'absolute',
                left: rightCoords.x - HANDLE_SIZE / 2,
                top: layout.y,
                width: HANDLE_SIZE,
                height: layout.height,
              },
            ]}
          />
        </GestureDetector>,
      );
    } else if (cropMode === CropMode.RECTANGULAR) {
      // Corner handles for rectangular crop
      const corners = [
        {
          key: 'topLeft',
          coords: getScreenCoords(cropLeft.value, cropTop.value),
          gesture: 'topLeft' as const,
        },
        {
          key: 'topRight',
          coords: getScreenCoords(cropRight.value, cropTop.value),
          gesture: 'topRight' as const,
        },
        {
          key: 'bottomLeft',
          coords: getScreenCoords(cropLeft.value, cropBottom.value),
          gesture: 'bottomLeft' as const,
        },
        {
          key: 'bottomRight',
          coords: getScreenCoords(cropRight.value, cropBottom.value),
          gesture: 'bottomRight' as const,
        },
      ];

      corners.forEach(({ key, coords, gesture }) => {
        handles.push(
          <GestureDetector key={key} gesture={createHandleGesture(gesture)}>
            <Animated.View
              style={[
                styles.cornerHandle,
                {
                  position: 'absolute',
                  left: coords.x - HANDLE_SIZE / 2,
                  top: coords.y - HANDLE_SIZE / 2,
                  width: HANDLE_SIZE,
                  height: HANDLE_SIZE,
                },
              ]}
            />
          </GestureDetector>,
        );
      });
    }

    return handles;
  }, [
    imageLayout.current,
    cropMode,
    cropLeft.value,
    cropTop.value,
    cropRight.value,
    cropBottom.value,
    createHandleGesture,
  ]);

  if (!skiaImage) {
    return <View style={styles.container} />;
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <View
        style={styles.canvasContainer}
        onLayout={event => {
          const { width, height } = event.nativeEvent.layout;
          setContainerSize({ width, height });
        }}>
        <Canvas style={StyleSheet.absoluteFillObject}>
          {/* Background image */}
          <SkiaImage
            image={skiaImage}
            rect={Skia.XYWHRect(
              imageLayout.current.x,
              imageLayout.current.y,
              imageLayout.current.width,
              imageLayout.current.height,
            )}
            fit="contain"
          />

          {/* Crop overlay */}
          <CropOverlay
            imageLayout={imageLayout}
            cropLeft={cropLeft}
            cropTop={cropTop}
            cropRight={cropRight}
            cropBottom={cropBottom}
            cropMode={cropMode}
          />
        </Canvas>

        {/* Render handles */}
        {renderHandles}
      </View>
    </GestureHandlerRootView>
  );
};

// Helper component for crop overlay rendering
interface CropOverlayProps {
  imageLayout: any;
  cropLeft: any;
  cropTop: any;
  cropRight: any;
  cropBottom: any;
  cropMode: CropMode;
}

const CropOverlay: React.FC<CropOverlayProps> = ({
  imageLayout,
  cropLeft,
  cropTop,
  cropRight,
  cropBottom,
  cropMode,
}) => {
  const overlayRects = useComputedValue(() => {
    const layout = imageLayout.current;
    if (layout.scale === 0) return [];

    const cropLeftScreen = layout.x + cropLeft.value * layout.scale;
    const cropTopScreen = layout.y + cropTop.value * layout.scale;
    const cropRightScreen = layout.x + cropRight.value * layout.scale;
    const cropBottomScreen = layout.y + cropBottom.value * layout.scale;

    const rects: any[] = [];

    if (cropMode === CropMode.HORIZONTAL_STRIP) {
      rects.push(
        {
          key: 'top',
          x: layout.x,
          y: layout.y,
          width: layout.width,
          height: cropTopScreen - layout.y,
        },
        {
          key: 'bottom',
          x: layout.x,
          y: cropBottomScreen,
          width: layout.width,
          height: layout.y + layout.height - cropBottomScreen,
        },
        {
          key: 'topBorder',
          x: layout.x,
          y: cropTopScreen,
          width: layout.width,
          height: 2,
          color: BORDER_COLOR,
        },
        {
          key: 'bottomBorder',
          x: layout.x,
          y: cropBottomScreen - 2,
          width: layout.width,
          height: 2,
          color: BORDER_COLOR,
        },
      );
    } else if (cropMode === CropMode.VERTICAL_STRIP) {
      rects.push(
        {
          key: 'left',
          x: layout.x,
          y: layout.y,
          width: cropLeftScreen - layout.x,
          height: layout.height,
        },
        {
          key: 'right',
          x: cropRightScreen,
          y: layout.y,
          width: layout.x + layout.width - cropRightScreen,
          height: layout.height,
        },
        {
          key: 'leftBorder',
          x: cropLeftScreen,
          y: layout.y,
          width: 2,
          height: layout.height,
          color: BORDER_COLOR,
        },
        {
          key: 'rightBorder',
          x: cropRightScreen - 2,
          y: layout.y,
          width: 2,
          height: layout.height,
          color: BORDER_COLOR,
        },
      );
    } else if (cropMode === CropMode.RECTANGULAR) {
      rects.push(
        {
          key: 'top',
          x: layout.x,
          y: layout.y,
          width: layout.width,
          height: cropTopScreen - layout.y,
        },
        {
          key: 'bottom',
          x: layout.x,
          y: cropBottomScreen,
          width: layout.width,
          height: layout.y + layout.height - cropBottomScreen,
        },
        {
          key: 'left',
          x: layout.x,
          y: cropTopScreen,
          width: cropLeftScreen - layout.x,
          height: cropBottomScreen - cropTopScreen,
        },
        {
          key: 'right',
          x: cropRightScreen,
          y: cropTopScreen,
          width: layout.x + layout.width - cropRightScreen,
          height: cropBottomScreen - cropTopScreen,
        },
      );
    }

    return rects;
  }, [imageLayout, cropLeft, cropTop, cropRight, cropBottom, cropMode]);

  return (
    <>
      {overlayRects.current.map((rect: any) => (
        <Rect
          key={rect.key}
          x={rect.x}
          y={rect.y}
          width={rect.width}
          height={rect.height}
          color={rect.color || OVERLAY_COLOR}
        />
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  canvasContainer: {
    flex: 1,
  },
  horizontalHandle: {
    backgroundColor: 'transparent',
    borderTopWidth: 2,
    borderTopColor: BORDER_COLOR,
  },
  verticalHandle: {
    backgroundColor: 'transparent',
    borderLeftWidth: 2,
    borderLeftColor: BORDER_COLOR,
  },
  cornerHandle: {
    backgroundColor: BORDER_COLOR,
    borderRadius: HANDLE_SIZE / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
});

export default CropPreview;
