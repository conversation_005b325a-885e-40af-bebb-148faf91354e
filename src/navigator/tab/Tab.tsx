import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, fonts } from '../../theme';
import { useAppSelector } from '../../utils/store';

// Import MiddleCrop views
import Home from '../../views/Home';
import History from '../../views/History';
import Settings from '../../views/Settings';

// Import types
import type { TabParamList } from '../../modules/crop/crop.typeDefs';

const Tab = createBottomTabNavigator<TabParamList>();

export default function TabNavigator() {
  const isPro = useAppSelector(state => state.settings.isPro);
  const remainingCrops = useAppSelector(state => {
    const cropsToday = state.crop.cropsToday;
    const dailyLimit = state.settings.dailyCropLimit;
    if (isPro || dailyLimit === Infinity) return Infinity;
    return Math.max(0, dailyLimit - cropsToday);
  });
  const historyCount = useAppSelector(state => state.crop.cropHistory.length);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: {
          backgroundColor: colors.surface || colors.background,
          borderTopColor: colors.border || '#E5E5E5',
          borderTopWidth: 1,
          paddingBottom: Platform.OS === 'ios' ? 20 : 8,
          paddingTop: 8,
          height: Platform.OS === 'ios' ? 85 : 65,
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        tabBarActiveTintColor: colors.primary || '#1E40AF',
        tabBarInactiveTintColor: colors.textSecondary || '#6b7280',
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
          fontFamily: fonts.regular,
        },
        tabBarIconStyle: {
          marginTop: 4,
        },
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'crop' : 'crop-outline';
              break;
            case 'History':
              iconName = focused ? 'time' : 'time-outline';
              break;
            case 'Settings':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size || 24} color={color} />;
        },
      })}>
      <Tab.Screen
        name="Home"
        component={Home}
        options={{
          title: 'Crop',
          tabBarBadge: !isPro && remainingCrops === 0 ? '!' : undefined,
          tabBarBadgeStyle: {
            backgroundColor: colors.error || '#DC2626',
            color: 'white',
            fontSize: 10,
            fontWeight: 'bold',
          },
        }}
      />
      <Tab.Screen
        name="History"
        component={History}
        options={{
          title: 'History',
          tabBarBadge:
            historyCount > 0 && historyCount <= 99
              ? historyCount
              : historyCount > 99
                ? '99+'
                : undefined,
          tabBarBadgeStyle: {
            backgroundColor: colors.primary || '#1E40AF',
            color: 'white',
            fontSize: 10,
            fontWeight: 'bold',
          },
        }}
      />
      <Tab.Screen
        name="Settings"
        component={Settings}
        options={{
          title: isPro ? 'Pro' : 'Settings',
          tabBarBadge: !isPro ? '✨' : undefined,
          tabBarBadgeStyle: {
            backgroundColor: colors.secondary || '#7C3AED',
            color: 'white',
            fontSize: 8,
            fontWeight: 'bold',
          },
        }}
      />
    </Tab.Navigator>
  );
}
