import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, Platform, StatusBar } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import * as SplashScreen from 'expo-splash-screen';
import { Ionicons } from '@expo/vector-icons';

// Import views
import Home from '../views/Home';
import History from '../views/History';
import Settings from '../views/Settings';

// Import utilities
import { useAppSelector, useAppDispatch, initializeStore } from '../utils/store';
import { loadPersistedCropData, resetDailyCount } from '../modules/crop/crop.slice';
import { loadPersistedSettings } from '../modules/settings/settings.slice';
import { initializePurchases, loadPersistedPurchaseData } from '../modules/purchase/purchase.slice';

// Import types
import type { RootStackParamList, TabParamList } from '../modules/crop/crop.typeDefs';

import { colors, fonts } from '../theme';

// Create navigators
const RootStack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

// Keep splash screen visible while loading
SplashScreen.preventAutoHideAsync();

// Pro Upgrade Screen Component
function ProUpgradeScreen() {
  const dispatch = useAppDispatch();
  const { purchasePro, restorePurchases } = require('../modules/purchase/purchase.slice');
  const purchaseStatus = useAppSelector(state => state.purchase);
  const productInfo = useAppSelector(state => {
    const product = state.purchase.products.find(p => p.identifier === 'middlecrop_pro_299');
    return (
      product ?? { price: '$2.99', title: 'MiddleCrop Pro', description: 'Unlock unlimited crops' }
    );
  });

  return (
    <SafeAreaProvider
      style={{
        flex: 1,
        backgroundColor: colors.background,
        padding: 20,
        justifyContent: 'center',
      }}>
      <View style={{ alignItems: 'center' }}>
        <Text style={{ fontSize: 28, fontWeight: 'bold', color: colors.text, marginBottom: 16 }}>
          Upgrade to MiddleCrop Pro
        </Text>
        <Text
          style={{
            fontSize: 18,
            color: colors.textSecondary,
            textAlign: 'center',
            marginBottom: 24,
          }}>
          {productInfo.description}
        </Text>

        <View style={{ marginBottom: 32, alignSelf: 'stretch' }}>
          <Text style={{ fontSize: 16, color: colors.text, marginBottom: 8 }}>
            ✓ Unlimited daily crops
          </Text>
          <Text style={{ fontSize: 16, color: colors.text, marginBottom: 8 }}>
            ✓ All export formats (PNG, WEBP, JPEG)
          </Text>
          <Text style={{ fontSize: 16, color: colors.text, marginBottom: 8 }}>
            ✓ Quality adjustment controls
          </Text>
          <Text style={{ fontSize: 16, color: colors.text, marginBottom: 8 }}>
            ✓ Unlimited crop history
          </Text>
          <Text style={{ fontSize: 16, color: colors.text, marginBottom: 8 }}>
            ✓ Priority support
          </Text>
        </View>

        <TouchableOpacity
          style={{
            backgroundColor: colors.primary,
            paddingHorizontal: 32,
            paddingVertical: 16,
            borderRadius: 12,
            marginBottom: 16,
            alignSelf: 'stretch',
          }}
          onPress={() => dispatch(purchasePro())}
          disabled={purchaseStatus.isLoading}>
          <Text style={{ color: 'white', fontSize: 18, fontWeight: 'bold', textAlign: 'center' }}>
            {purchaseStatus.isLoading ? 'Processing...' : `Upgrade for ${productInfo.price}`}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            backgroundColor: 'transparent',
            paddingHorizontal: 16,
            paddingVertical: 12,
            borderRadius: 8,
          }}
          onPress={() => dispatch(restorePurchases())}
          disabled={purchaseStatus.isRestoring}>
          <Text style={{ color: colors.primary, fontSize: 16, textAlign: 'center' }}>
            {purchaseStatus.isRestoring ? 'Restoring...' : 'Restore Purchases'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaProvider>
  );
}

// Tab Navigator
function TabNavigator() {
  const isPro = useAppSelector(state => state.settings.isPro);
  const remainingCrops = useAppSelector(state => {
    const cropsToday = state.crop.cropsToday;
    const dailyLimit = state.settings.dailyCropLimit;
    if (isPro || dailyLimit === Infinity) return Infinity;
    return Math.max(0, dailyLimit - cropsToday);
  });
  const historyCount = useAppSelector(state => state.crop.cropHistory.length);

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarStyle: {
          backgroundColor: colors.surface || colors.background,
          borderTopColor: colors.border || '#E5E5E5',
          borderTopWidth: 1,
          paddingBottom: Platform.OS === 'ios' ? 20 : 8,
          paddingTop: 8,
          height: Platform.OS === 'ios' ? 85 : 65,
        },
        tabBarActiveTintColor: colors.primary || '#007AFF',
        tabBarInactiveTintColor: colors.textSecondary || '#8E8E93',
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
          fontFamily: fonts.regular,
        },
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'crop' : 'crop-outline';
              break;
            case 'History':
              iconName = focused ? 'time' : 'time-outline';
              break;
            case 'Settings':
              iconName = focused ? 'settings' : 'settings-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
      })}>
      <Tab.Screen
        name="Home"
        component={Home}
        options={{
          title: 'Crop',
          tabBarBadge: !isPro && remainingCrops === 0 ? '!' : undefined,
        }}
      />
      <Tab.Screen
        name="History"
        component={History}
        options={{
          title: 'History',
          tabBarBadge: historyCount > 0 && historyCount <= 99 ? historyCount : undefined,
        }}
      />
      <Tab.Screen
        name="Settings"
        component={Settings}
        options={{
          title: isPro ? 'Pro' : 'Settings',
          tabBarBadge: !isPro ? '✨' : undefined,
        }}
      />
    </Tab.Navigator>
  );
}

// Root Navigator
function RootNavigator() {
  return (
    <RootStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: Platform.OS === 'ios',
        animation: Platform.OS === 'ios' ? 'slide_from_right' : 'fade',
      }}>
      <RootStack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          gestureEnabled: false,
        }}
      />
      <RootStack.Screen
        name="ProUpgrade"
        component={ProUpgradeScreen}
        options={{
          presentation: 'modal',
          gestureEnabled: true,
          headerShown: true,
          headerTitle: 'Upgrade to Pro',
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerTintColor: colors.text,
          headerTitleStyle: {
            fontFamily: fonts.semibold,
            fontSize: 18,
          },
        }}
      />
    </RootStack.Navigator>
  );
}

// App Initialization Component
function AppInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        console.log('MiddleCrop: Starting app initialization...');

        // Initialize store and load persisted data
        await initializeStore();

        // Load all persisted data in parallel
        await Promise.all([
          dispatch(loadPersistedCropData()),
          dispatch(loadPersistedSettings()),
          dispatch(loadPersistedPurchaseData()),
        ]);

        // Initialize RevenueCat purchases
        await dispatch(initializePurchases());

        // Reset daily count if needed
        dispatch(resetDailyCount());

        console.log('MiddleCrop: App initialization complete');

        // Small delay to ensure smooth loading
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.warn('Failed to initialize MiddleCrop:', error);
      } finally {
        setIsReady(true);
        await SplashScreen.hideAsync();
      }
    }

    prepare();
  }, [dispatch]);

  if (!isReady) {
    return null; // Keep splash screen visible
  }

  return <>{children}</>;
}

// Main Navigator Component
export default function Navigator() {
  const theme = useAppSelector(state => state.settings.theme);

  // Configure status bar based on theme
  const statusBarStyle = useMemo(() => {
    if (theme === 'dark') return 'light-content';
    if (theme === 'light') return 'dark-content';
    return 'default'; // System theme
  }, [theme]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <StatusBar
          barStyle={statusBarStyle}
          backgroundColor={colors.background}
          translucent={false}
        />
        <NavigationContainer>
          <AppInitializer>
            <RootNavigator />
          </AppInitializer>
        </NavigationContainer>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
